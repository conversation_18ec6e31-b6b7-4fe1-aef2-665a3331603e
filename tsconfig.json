{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "isolatedModules": true, "strict": true, "jsx": "preserve", "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"@/*": ["./src/*"], "@themeConfig": ["./themeConfig.ts"], "@layouts/*": ["./src/@layouts/*"], "@layouts": ["./src/@layouts"], "@core/*": ["./src/@core/*"], "@core": ["./src/@core"], "@images/*": ["./src/assets/images/*"], "@styles/*": ["./src/assets/styles/*"], "@validators": ["./src/@core/utils/validators"], "@db/*": ["./src/plugins/fake-api/handlers/*"], "@api-utils/*": ["./src/plugins/fake-api/utils/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "skipLibCheck": true, "types": ["vite/client", "unplugin-vue-router/client", "vite-plugin-vue-layouts/client", "sweetalert2"]}, "include": ["./typed-router.d.ts", "./vite.config.*", "./env.d.ts", "./shims.d.ts", "./src/**/*", "./src/**/*.vue", "./themeConfig.ts", "./auto-imports.d.ts", "./components.d.ts"], "exclude": ["./dist", "./node_modules"]}