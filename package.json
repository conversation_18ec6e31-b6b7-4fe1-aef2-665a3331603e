{"name": "pension-admin", "version": "9.3.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 5178", "build": "vite build", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "build:icons": "tsx src/plugins/iconify/build-icons.ts", "msw:init": "msw init public/ --save", "postinstall": "npm run build:icons && npm run msw:init", "codegen": "npx graphql-codegen"}, "dependencies": {"@apollo/client": "^3.13.1", "@casl/ability": "6.7.2", "@casl/vue": "2.2.2", "@floating-ui/dom": "1.6.8", "@formkit/drag-and-drop": "0.1.6", "@sindresorhus/is": "7.0.1", "@tiptap/extension-highlight": "^2.10.3", "@tiptap/extension-image": "^2.10.3", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@tiptap/vue-3": "^2.10.3", "@vue/apollo-composable": "^4.0.0-beta.12", "@vueuse/core": "10.11.1", "@vueuse/math": "10.11.1", "apexcharts": "^4.0.0", "chart.js": "4.4.7", "cookie-es": "1.2.2", "date-fns": "^4.1.0", "destr": "2.0.3", "eslint-plugin-regexp": "2.7.0", "firebase": "^11.4.0", "graphql": "^16.10.0", "graphql-tag": "^2.12.6", "graphql-ws": "^6.0.4", "jwt-decode": "4.0.0", "lodash.clonedeep": "^4.5.0", "mapbox-gl": "3.5.2", "ofetch": "1.4.1", "pinia": "2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "prismjs": "1.29.0", "roboto-fontface": "0.10.0", "shepherd.js": "13.0.3", "sweetalert2": "^11.17.2", "swiper": "11.1.15", "ufo": "1.5.4", "unplugin-vue-define-options": "1.5.3", "vue": "3.5.13", "vue-chartjs": "5.3.2", "vue-flatpickr-component": "11.0.5", "vue-i18n": "10.0.4", "vue-prism-component": "2.0.0", "vue-router": "4.5.0", "vue3-apexcharts": "1.8.0", "vue3-perfect-scrollbar": "2.0.0", "vuetify": "3.7.5", "webfontloader": "1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "0.43.1", "@antfu/utils": "0.7.10", "@fullcalendar/core": "6.1.15", "@fullcalendar/daygrid": "6.1.15", "@fullcalendar/interaction": "6.1.15", "@fullcalendar/list": "6.1.15", "@fullcalendar/timegrid": "6.1.15", "@fullcalendar/vue3": "6.1.15", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/client-preset": "^4.1.0", "@graphql-codegen/typescript-vue-apollo": "^4.1.0", "@iconify-json/fa": "1.2.1", "@iconify-json/mdi": "1.2.2", "@iconify-json/tabler": "1.2.13", "@iconify/tools": "4.1.1", "@iconify/utils": "2.2.1", "@iconify/vue": "4.1.2", "@intlify/unplugin-vue-i18n": "5.3.1", "@stylistic/stylelint-config": "1.0.1", "@stylistic/stylelint-plugin": "2.1.3", "@tiptap/extension-character-count": "^2.10.3", "@tiptap/extension-placeholder": "^2.10.3", "@tiptap/extension-subscript": "^2.10.3", "@tiptap/extension-superscript": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@types/lodash.clonedeep": "^4.5.9", "@types/mapbox-gl": "3.4.1", "@types/node": "22.10.2", "@types/webfontloader": "1.6.38", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@videojs-player/vue": "1.0.0", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "eslint": "8.57.1", "eslint-config-airbnb-base": "15.0.0", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-case-police": "0.6.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-promise": "6.6.0", "eslint-plugin-regex": "1.10.0", "eslint-plugin-sonarjs": "0.24.0", "eslint-plugin-unicorn": "51.0.1", "eslint-plugin-vue": "9.32.0", "msw": "2.3.4", "postcss-html": "1.7.0", "postcss-scss": "4.0.9", "sass": "1.76.0", "shiki": "1.24.2", "stylelint": "16.8.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.1.0", "stylelint-use-logical-spec": "5.0.1", "tsx": "4.19.2", "type-fest": "4.30.2", "typescript": "5.7.2", "unplugin-auto-import": "0.18.6", "unplugin-vue-components": "0.27.5", "unplugin-vue-router": "0.8.8", "video.js": "^7.0.0", "vite": "5.4.11", "vite-plugin-vue-devtools": "7.3.7", "vite-plugin-vue-layouts": "0.11.0", "vite-plugin-vuetify": "2.0.3", "vite-svg-loader": "5.1.0", "vue-shepherd": "3.0.0", "vue-tsc": "2.1.10"}, "resolutions": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}, "overrides": {"postcss": "^8", "@tiptap/core": "^2", "@types/video.js": "^7"}, "msw": {"workerDirectory": "public"}}