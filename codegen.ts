import type { CodegenConfig } from '@graphql-codegen/cli'
import { DEV_API_URL } from './src/constants/app'

const config: CodegenConfig = {
  schema: DEV_API_URL,
  documents: ['src/api/graphql/**/*.ts', 'src/**/*.vue', 'src/**/*.ts'],
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: {
    './src/gql/': {
      plugins: ['typescript', 'typescript-operations', 'typescript-vue-apollo'],
      config: {
        withCompositionFunctions: true,
        vueApolloComposableImportFrom: 'vue',
      },
      preset: 'client',
    },
  },
}

export default config
