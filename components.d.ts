/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccrualPeriodCalculation: typeof import('./src/components/participants/pensionCalculation/AccrualPeriodCalculation.vue')['default']
    AddAuthenticatorAppDialog: typeof import('./src/components/dialogs/AddAuthenticatorAppDialog.vue')['default']
    AddEditAddressDialog: typeof import('./src/components/dialogs/AddEditAddressDialog.vue')['default']
    AddEditPermissionDialog: typeof import('./src/components/dialogs/AddEditPermissionDialog.vue')['default']
    AddEditRoleDialog: typeof import('./src/components/dialogs/AddEditRoleDialog.vue')['default']
    AddParticipantForm: typeof import('./src/components/participants/AddParticipantForm.vue')['default']
    AddPaymentMethodDialog: typeof import('./src/components/dialogs/AddPaymentMethodDialog.vue')['default']
    Address: typeof import('./src/components/participants/basicInfo/Address.vue')['default']
    AppAutocomplete: typeof import('./src/@core/components/app-form-elements/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./src/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./src/@core/components/cards/AppCardActions.vue')['default']
    AppCardCode: typeof import('./src/@core/components/cards/AppCardCode.vue')['default']
    AppCombobox: typeof import('./src/@core/components/app-form-elements/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./src/@core/components/app-form-elements/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./src/@core/components/AppDrawerHeaderSection.vue')['default']
    AppLoadingIndicator: typeof import('./src/components/AppLoadingIndicator.vue')['default']
    AppPricing: typeof import('./src/components/AppPricing.vue')['default']
    ApproveRejectCertFields: typeof import('./src/components/certified-data/ApproveRejectCertFields.vue')['default']
    ApproveRejectFieldDialog: typeof import('./src/components/certified-data/ApproveRejectFieldDialog.vue')['default']
    AppSearchHeader: typeof import('./src/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./src/@core/components/app-form-elements/AppSelect.vue')['default']
    AppStepper: typeof import('./src/@core/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./src/@core/components/app-form-elements/AppTextarea.vue')['default']
    AppTextField: typeof import('./src/@core/components/app-form-elements/AppTextField.vue')['default']
    BasicInformation: typeof import('./src/components/participants/basicInfo/BasicInformation.vue')['default']
    BasicInformationOldTable: typeof import('./src/components/participants/basicInfo/BasicInformationOldTable.vue')['default']
    BuyNow: typeof import('./src/@core/components/BuyNow.vue')['default']
    CardAddEditDialog: typeof import('./src/components/dialogs/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./src/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./src/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./src/@core/components/CardStatisticsVerticalSimple.vue')['default']
    CertificationActions: typeof import('./src/components/certified-data/CertificationActions.vue')['default']
    CertifiedAccrualPeriodCalculation: typeof import('./src/components/certified-data/pensionCalculation/CertifiedAccrualPeriodCalculation.vue')['default']
    CertifiedAddress: typeof import('./src/components/certified-data/basicInfo/CertifiedAddress.vue')['default']
    CertifiedBasicInformation: typeof import('./src/components/certified-data/basicInfo/CertifiedBasicInformation.vue')['default']
    CertifiedChildren: typeof import('./src/components/certified-data/basicInfo/CertifiedChildren.vue')['default']
    CertifiedDataList: typeof import('./src/components/certified-data/CertifiedDataList.vue')['default']
    CertifiedExPartners: typeof import('./src/components/certified-data/basicInfo/CertifiedExPartners.vue')['default']
    CertifiedIndexationBegining: typeof import('./src/components/certified-data/pensionCalculation/CertifiedIndexationBegining.vue')['default']
    CertifiedMaritalStatus: typeof import('./src/components/certified-data/basicInfo/CertifiedMaritalStatus.vue')['default']
    CertifiedParticipantChangeTable: typeof import('./src/components/change-proposal/requested/CertifiedParticipantChangeTable.vue')['default']
    CertifiedParticipantInformation: typeof import('./src/components/certified-data/basicInfo/CertifiedParticipantInformation.vue')['default']
    CertifiedPartTimePercentage: typeof import('./src/components/certified-data/CertifiedPartTimePercentage.vue')['default']
    CertifiedPensionBaseHeader: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionBaseHeader.vue')['default']
    CertifiedPensionCalculationBase: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionCalculationBase.vue')['default']
    CertifiedPensionPrimaryCalc: typeof import('./src/components/certified-data/salaryPension/CertifiedPensionPrimaryCalc.vue')['default']
    CertifiedPensionsAccrual: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsAccrual.vue')['default']
    CertifiedPensionsAfterCorrections: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsAfterCorrections.vue')['default']
    CertifiedPensionsAsPerReferenceDate: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsAsPerReferenceDate.vue')['default']
    CertifiedPensionsCalcStrip: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsCalcStrip.vue')['default']
    CertifiedPensionsEndOfPreviousCalenderYear: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsEndOfPreviousCalenderYear.vue')['default']
    CertifiedPensionTableLayout: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionTableLayout.vue')['default']
    CertifiedPensionTableRow: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionTableRow.vue')['default']
    CertifiedSalaryEntityDialog: typeof import('./src/components/certified-data/salaryPension/CertifiedSalaryEntityDialog.vue')['default']
    CertifiedSalaryEntries: typeof import('./src/components/certified-data/CertifiedSalaryEntries.vue')['default']
    CertifiedSalaryPension: typeof import('./src/components/certified-data/salaryPension/CertifiedSalaryPension.vue')['default']
    CertifiedSalaryPensionBase: typeof import('./src/components/certified-data/salaryPension/CertifiedSalaryPensionBase.vue')['default']
    ChangeFilter: typeof import('./src/components/change-proposal/ChangeFilter.vue')['default']
    ChangeFilterTest: typeof import('./src/components/change-proposal/ChangeFilterTest.vue')['default']
    Children: typeof import('./src/components/participants/basicInfo/Children.vue')['default']
    CompareChangesDialog: typeof import('./src/components/dialogs/CompareChangesDialog.vue')['default']
    CompareChangesDialog01: typeof import('./src/components/dialogs/CompareChangesDialog01.vue')['default']
    CompareContents: typeof import('./src/components/dialogs/CompareContents.vue')['default']
    ConfirmDialog: typeof import('./src/components/dialogs/ConfirmDialog.vue')['default']
    CreateAppDialog: typeof import('./src/components/dialogs/CreateAppDialog.vue')['default']
    CustomCheckboxes: typeof import('./src/@core/components/app-form-elements/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./src/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./src/@core/components/app-form-elements/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithImage.vue')['default']
    DemoAlertBasic: typeof import('./src/views/demos/components/alert/DemoAlertBasic.vue')['default']
    DemoAlertBorder: typeof import('./src/views/demos/components/alert/DemoAlertBorder.vue')['default']
    DemoAlertClosable: typeof import('./src/views/demos/components/alert/DemoAlertClosable.vue')['default']
    DemoAlertColoredBorder: typeof import('./src/views/demos/components/alert/DemoAlertColoredBorder.vue')['default']
    DemoAlertColors: typeof import('./src/views/demos/components/alert/DemoAlertColors.vue')['default']
    DemoAlertDensity: typeof import('./src/views/demos/components/alert/DemoAlertDensity.vue')['default']
    DemoAlertElevation: typeof import('./src/views/demos/components/alert/DemoAlertElevation.vue')['default']
    DemoAlertIcons: typeof import('./src/views/demos/components/alert/DemoAlertIcons.vue')['default']
    DemoAlertOutlined: typeof import('./src/views/demos/components/alert/DemoAlertOutlined.vue')['default']
    DemoAlertProminent: typeof import('./src/views/demos/components/alert/DemoAlertProminent.vue')['default']
    DemoAlertTonal: typeof import('./src/views/demos/components/alert/DemoAlertTonal.vue')['default']
    DemoAlertType: typeof import('./src/views/demos/components/alert/DemoAlertType.vue')['default']
    DemoAlertVModelSupport: typeof import('./src/views/demos/components/alert/DemoAlertVModelSupport.vue')['default']
    DemoAutocompleteAsyncItems: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteAsyncItems.vue')['default']
    DemoAutocompleteBasic: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteBasic.vue')['default']
    DemoAutocompleteChips: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteChips.vue')['default']
    DemoAutocompleteClearable: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteClearable.vue')['default']
    DemoAutocompleteCustomFilter: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteCustomFilter.vue')['default']
    DemoAutocompleteDensity: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteDensity.vue')['default']
    DemoAutocompleteMultiple: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteMultiple.vue')['default']
    DemoAutocompleteSlots: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteSlots.vue')['default']
    DemoAutocompleteStateSelector: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteStateSelector.vue')['default']
    DemoAutocompleteValidation: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteValidation.vue')['default']
    DemoAutocompleteVariant: typeof import('./src/views/demos/forms/form-elements/autocomplete/DemoAutocompleteVariant.vue')['default']
    DemoAvatarColors: typeof import('./src/views/demos/components/avatar/DemoAvatarColors.vue')['default']
    DemoAvatarGroup: typeof import('./src/views/demos/components/avatar/DemoAvatarGroup.vue')['default']
    DemoAvatarIcons: typeof import('./src/views/demos/components/avatar/DemoAvatarIcons.vue')['default']
    DemoAvatarImages: typeof import('./src/views/demos/components/avatar/DemoAvatarImages.vue')['default']
    DemoAvatarRounded: typeof import('./src/views/demos/components/avatar/DemoAvatarRounded.vue')['default']
    DemoAvatarSizes: typeof import('./src/views/demos/components/avatar/DemoAvatarSizes.vue')['default']
    DemoAvatarTonal: typeof import('./src/views/demos/components/avatar/DemoAvatarTonal.vue')['default']
    DemoBadgeAvatarStatus: typeof import('./src/views/demos/components/badge/DemoBadgeAvatarStatus.vue')['default']
    DemoBadgeColor: typeof import('./src/views/demos/components/badge/DemoBadgeColor.vue')['default']
    DemoBadgeDynamicNotifications: typeof import('./src/views/demos/components/badge/DemoBadgeDynamicNotifications.vue')['default']
    DemoBadgeIcon: typeof import('./src/views/demos/components/badge/DemoBadgeIcon.vue')['default']
    DemoBadgeMaximumValue: typeof import('./src/views/demos/components/badge/DemoBadgeMaximumValue.vue')['default']
    DemoBadgePosition: typeof import('./src/views/demos/components/badge/DemoBadgePosition.vue')['default']
    DemoBadgeShowOnHover: typeof import('./src/views/demos/components/badge/DemoBadgeShowOnHover.vue')['default']
    DemoBadgeStyle: typeof import('./src/views/demos/components/badge/DemoBadgeStyle.vue')['default']
    DemoBadgeTabs: typeof import('./src/views/demos/components/badge/DemoBadgeTabs.vue')['default']
    DemoBadgeTonal: typeof import('./src/views/demos/components/badge/DemoBadgeTonal.vue')['default']
    DemoButtonBlock: typeof import('./src/views/demos/components/button/DemoButtonBlock.vue')['default']
    DemoButtonColors: typeof import('./src/views/demos/components/button/DemoButtonColors.vue')['default']
    DemoButtonFlat: typeof import('./src/views/demos/components/button/DemoButtonFlat.vue')['default']
    DemoButtonGroup: typeof import('./src/views/demos/components/button/DemoButtonGroup.vue')['default']
    DemoButtonIcon: typeof import('./src/views/demos/components/button/DemoButtonIcon.vue')['default']
    DemoButtonIconOnly: typeof import('./src/views/demos/components/button/DemoButtonIconOnly.vue')['default']
    DemoButtonLink: typeof import('./src/views/demos/components/button/DemoButtonLink.vue')['default']
    DemoButtonLoaders: typeof import('./src/views/demos/components/button/DemoButtonLoaders.vue')['default']
    DemoButtonOutlined: typeof import('./src/views/demos/components/button/DemoButtonOutlined.vue')['default']
    DemoButtonPlain: typeof import('./src/views/demos/components/button/DemoButtonPlain.vue')['default']
    DemoButtonRounded: typeof import('./src/views/demos/components/button/DemoButtonRounded.vue')['default']
    DemoButtonRouter: typeof import('./src/views/demos/components/button/DemoButtonRouter.vue')['default']
    DemoButtonSizing: typeof import('./src/views/demos/components/button/DemoButtonSizing.vue')['default']
    DemoButtonText: typeof import('./src/views/demos/components/button/DemoButtonText.vue')['default']
    DemoButtonTonal: typeof import('./src/views/demos/components/button/DemoButtonTonal.vue')['default']
    DemoCheckboxBasic: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxBasic.vue')['default']
    DemoCheckboxCheckboxValue: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxCheckboxValue.vue')['default']
    DemoCheckboxColors: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxColors.vue')['default']
    DemoCheckboxDensity: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxDensity.vue')['default']
    DemoCheckboxIcon: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxIcon.vue')['default']
    DemoCheckboxInlineTextField: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxInlineTextField.vue')['default']
    DemoCheckboxLabelSlot: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxLabelSlot.vue')['default']
    DemoCheckboxModelAsArray: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxModelAsArray.vue')['default']
    DemoCheckboxStates: typeof import('./src/views/demos/forms/form-elements/checkbox/DemoCheckboxStates.vue')['default']
    DemoChipClosable: typeof import('./src/views/demos/components/chip/DemoChipClosable.vue')['default']
    DemoChipColor: typeof import('./src/views/demos/components/chip/DemoChipColor.vue')['default']
    DemoChipElevated: typeof import('./src/views/demos/components/chip/DemoChipElevated.vue')['default']
    DemoChipExpandable: typeof import('./src/views/demos/components/chip/DemoChipExpandable.vue')['default']
    DemoChipInSelects: typeof import('./src/views/demos/components/chip/DemoChipInSelects.vue')['default']
    DemoChipOutlined: typeof import('./src/views/demos/components/chip/DemoChipOutlined.vue')['default']
    DemoChipRounded: typeof import('./src/views/demos/components/chip/DemoChipRounded.vue')['default']
    DemoChipSizes: typeof import('./src/views/demos/components/chip/DemoChipSizes.vue')['default']
    DemoChipWithAvatar: typeof import('./src/views/demos/components/chip/DemoChipWithAvatar.vue')['default']
    DemoChipWithIcon: typeof import('./src/views/demos/components/chip/DemoChipWithIcon.vue')['default']
    DemoComboboxBasic: typeof import('./src/views/demos/forms/form-elements/combobox/DemoComboboxBasic.vue')['default']
    DemoComboboxClearable: typeof import('./src/views/demos/forms/form-elements/combobox/DemoComboboxClearable.vue')['default']
    DemoComboboxDensity: typeof import('./src/views/demos/forms/form-elements/combobox/DemoComboboxDensity.vue')['default']
    DemoComboboxMultiple: typeof import('./src/views/demos/forms/form-elements/combobox/DemoComboboxMultiple.vue')['default']
    DemoComboboxNoDataWithChips: typeof import('./src/views/demos/forms/form-elements/combobox/DemoComboboxNoDataWithChips.vue')['default']
    DemoComboboxVariant: typeof import('./src/views/demos/forms/form-elements/combobox/DemoComboboxVariant.vue')['default']
    DemoCustomInputCustomCheckboxes: typeof import('./src/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomCheckboxes.vue')['default']
    DemoCustomInputCustomCheckboxesWithIcon: typeof import('./src/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomCheckboxesWithIcon.vue')['default']
    DemoCustomInputCustomCheckboxesWithImage: typeof import('./src/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomCheckboxesWithImage.vue')['default']
    DemoCustomInputCustomRadios: typeof import('./src/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomRadios.vue')['default']
    DemoCustomInputCustomRadiosWithIcon: typeof import('./src/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomRadiosWithIcon.vue')['default']
    DemoCustomInputCustomRadiosWithImage: typeof import('./src/views/demos/forms/form-elements/custom-input/DemoCustomInputCustomRadiosWithImage.vue')['default']
    DemoDataTableBasic: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableBasic.vue')['default']
    DemoDataTableCellSlot: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableCellSlot.vue')['default']
    DemoDataTableDense: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableDense.vue')['default']
    DemoDataTableExpandableRows: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableExpandableRows.vue')['default']
    DemoDataTableExternalPagination: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableExternalPagination.vue')['default']
    DemoDataTableFixedHeader: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableFixedHeader.vue')['default']
    DemoDataTableGroupingRows: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableGroupingRows.vue')['default']
    DemoDataTableKitchenSink: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableKitchenSink.vue')['default']
    DemoDataTableRowEditingViaDialog: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableRowEditingViaDialog.vue')['default']
    DemoDataTableRowSelection: typeof import('./src/views/demos/forms/tables/data-table/DemoDataTableRowSelection.vue')['default']
    DemoDateTimePickerBasic: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerBasic.vue')['default']
    DemoDateTimePickerDateAndTime: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerDateAndTime.vue')['default']
    DemoDateTimePickerDisabledRange: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerDisabledRange.vue')['default']
    DemoDateTimePickerHumanFriendly: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerHumanFriendly.vue')['default']
    DemoDateTimePickerInline: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerInline.vue')['default']
    DemoDateTimePickerMultipleDates: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerMultipleDates.vue')['default']
    DemoDateTimePickerRange: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerRange.vue')['default']
    DemoDateTimePickerTimePicker: typeof import('./src/views/demos/forms/form-elements/date-time-picker/DemoDateTimePickerTimePicker.vue')['default']
    DemoDialogBasic: typeof import('./src/views/demos/components/dialog/DemoDialogBasic.vue')['default']
    DemoDialogForm: typeof import('./src/views/demos/components/dialog/DemoDialogForm.vue')['default']
    DemoDialogFullscreen: typeof import('./src/views/demos/components/dialog/DemoDialogFullscreen.vue')['default']
    DemoDialogLoader: typeof import('./src/views/demos/components/dialog/DemoDialogLoader.vue')['default']
    DemoDialogNesting: typeof import('./src/views/demos/components/dialog/DemoDialogNesting.vue')['default']
    DemoDialogOverflowed: typeof import('./src/views/demos/components/dialog/DemoDialogOverflowed.vue')['default']
    DemoDialogPersistent: typeof import('./src/views/demos/components/dialog/DemoDialogPersistent.vue')['default']
    DemoDialogScrollable: typeof import('./src/views/demos/components/dialog/DemoDialogScrollable.vue')['default']
    DemoEditorBasicEditor: typeof import('./src/views/demos/forms/form-elements/editor/DemoEditorBasicEditor.vue')['default']
    DemoEditorCustomEditor: typeof import('./src/views/demos/forms/form-elements/editor/DemoEditorCustomEditor.vue')['default']
    DemoExpansionPanelAccordion: typeof import('./src/views/demos/components/expansion-panel/DemoExpansionPanelAccordion.vue')['default']
    DemoExpansionPanelBasic: typeof import('./src/views/demos/components/expansion-panel/DemoExpansionPanelBasic.vue')['default']
    DemoExpansionPanelCustomIcon: typeof import('./src/views/demos/components/expansion-panel/DemoExpansionPanelCustomIcon.vue')['default']
    DemoExpansionPanelInset: typeof import('./src/views/demos/components/expansion-panel/DemoExpansionPanelInset.vue')['default']
    DemoExpansionPanelModel: typeof import('./src/views/demos/components/expansion-panel/DemoExpansionPanelModel.vue')['default']
    DemoExpansionPanelPopout: typeof import('./src/views/demos/components/expansion-panel/DemoExpansionPanelPopout.vue')['default']
    DemoExpansionPanelWithBorder: typeof import('./src/views/demos/components/expansion-panel/DemoExpansionPanelWithBorder.vue')['default']
    DemoFileInputAccept: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputAccept.vue')['default']
    DemoFileInputBasic: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputBasic.vue')['default']
    DemoFileInputChips: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputChips.vue')['default']
    DemoFileInputCounter: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputCounter.vue')['default']
    DemoFileInputDensity: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputDensity.vue')['default']
    DemoFileInputLoading: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputLoading.vue')['default']
    DemoFileInputMultiple: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputMultiple.vue')['default']
    DemoFileInputPrependIcon: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputPrependIcon.vue')['default']
    DemoFileInputSelectionSlot: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputSelectionSlot.vue')['default']
    DemoFileInputShowSize: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputShowSize.vue')['default']
    DemoFileInputValidation: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputValidation.vue')['default']
    DemoFileInputVariant: typeof import('./src/views/demos/forms/form-elements/file-input/DemoFileInputVariant.vue')['default']
    DemoFormLayoutCollapsible: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutCollapsible.vue')['default']
    DemoFormLayoutFormHint: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutFormHint.vue')['default']
    DemoFormLayoutFormSticky: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutFormSticky.vue')['default']
    DemoFormLayoutFormValidation: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutFormValidation.vue')['default']
    DemoFormLayoutFormWithTabs: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutFormWithTabs.vue')['default']
    DemoFormLayoutHorizontalForm: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutHorizontalForm.vue')['default']
    DemoFormLayoutHorizontalFormWithIcons: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutHorizontalFormWithIcons.vue')['default']
    DemoFormLayoutMultipleColumn: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutMultipleColumn.vue')['default']
    DemoFormLayoutSticky: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutSticky.vue')['default']
    DemoFormLayoutVerticalForm: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutVerticalForm.vue')['default']
    DemoFormLayoutVerticalFormWithIcons: typeof import('./src/views/demos/forms/form-layout/DemoFormLayoutVerticalFormWithIcons.vue')['default']
    DemoFormValidationSimpleFormValidation: typeof import('./src/views/demos/forms/form-validation/DemoFormValidationSimpleFormValidation.vue')['default']
    DemoFormValidationValidatingMultipleRules: typeof import('./src/views/demos/forms/form-validation/DemoFormValidationValidatingMultipleRules.vue')['default']
    DemoFormValidationValidationTypes: typeof import('./src/views/demos/forms/form-validation/DemoFormValidationValidationTypes.vue')['default']
    DemoFormWizardIconsBasic: typeof import('./src/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsBasic.vue')['default']
    DemoFormWizardIconsModernBasic: typeof import('./src/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsModernBasic.vue')['default']
    DemoFormWizardIconsModernVertical: typeof import('./src/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsModernVertical.vue')['default']
    DemoFormWizardIconsValidation: typeof import('./src/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsValidation.vue')['default']
    DemoFormWizardIconsVertical: typeof import('./src/views/demos/forms/form-wizard/form-wizard-icons/DemoFormWizardIconsVertical.vue')['default']
    DemoFormWizardNumberdModernBasic: typeof import('./src/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberdModernBasic.vue')['default']
    DemoFormWizardNumberedBasic: typeof import('./src/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedBasic.vue')['default']
    DemoFormWizardNumberedModernVertical: typeof import('./src/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedModernVertical.vue')['default']
    DemoFormWizardNumberedValidation: typeof import('./src/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedValidation.vue')['default']
    DemoFormWizardNumberedVertical: typeof import('./src/views/demos/forms/form-wizard/form-wizard-numbered/DemoFormWizardNumberedVertical.vue')['default']
    DemoListActionAndItemGroup: typeof import('./src/views/demos/components/list/DemoListActionAndItemGroup.vue')['default']
    DemoListBasic: typeof import('./src/views/demos/components/list/DemoListBasic.vue')['default']
    DemoListDensity: typeof import('./src/views/demos/components/list/DemoListDensity.vue')['default']
    DemoListNav: typeof import('./src/views/demos/components/list/DemoListNav.vue')['default']
    DemoListProgressList: typeof import('./src/views/demos/components/list/DemoListProgressList.vue')['default']
    DemoListRounded: typeof import('./src/views/demos/components/list/DemoListRounded.vue')['default']
    DemoListShaped: typeof import('./src/views/demos/components/list/DemoListShaped.vue')['default']
    DemoListSubGroup: typeof import('./src/views/demos/components/list/DemoListSubGroup.vue')['default']
    DemoListThreeLine: typeof import('./src/views/demos/components/list/DemoListThreeLine.vue')['default']
    DemoListTwoLinesAndSubheader: typeof import('./src/views/demos/components/list/DemoListTwoLinesAndSubheader.vue')['default']
    DemoListUserList: typeof import('./src/views/demos/components/list/DemoListUserList.vue')['default']
    DemoMenuActivatorAndTooltip: typeof import('./src/views/demos/components/menu/DemoMenuActivatorAndTooltip.vue')['default']
    DemoMenuBasic: typeof import('./src/views/demos/components/menu/DemoMenuBasic.vue')['default']
    DemoMenuCustomTransitions: typeof import('./src/views/demos/components/menu/DemoMenuCustomTransitions.vue')['default']
    DemoMenuLocation: typeof import('./src/views/demos/components/menu/DemoMenuLocation.vue')['default']
    DemoMenuOpenOnHover: typeof import('./src/views/demos/components/menu/DemoMenuOpenOnHover.vue')['default']
    DemoMenuPopover: typeof import('./src/views/demos/components/menu/DemoMenuPopover.vue')['default']
    DemoOtpInputBasic: typeof import('./src/views/demos/forms/form-elements/otp-input/DemoOtpInputBasic.vue')['default']
    DemoOtpInputFinish: typeof import('./src/views/demos/forms/form-elements/otp-input/DemoOtpInputFinish.vue')['default']
    DemoOtpInputHidden: typeof import('./src/views/demos/forms/form-elements/otp-input/DemoOtpInputHidden.vue')['default']
    DemoPaginationBasic: typeof import('./src/views/demos/components/pagination/DemoPaginationBasic.vue')['default']
    DemoPaginationCircle: typeof import('./src/views/demos/components/pagination/DemoPaginationCircle.vue')['default']
    DemoPaginationColor: typeof import('./src/views/demos/components/pagination/DemoPaginationColor.vue')['default']
    DemoPaginationDisabled: typeof import('./src/views/demos/components/pagination/DemoPaginationDisabled.vue')['default']
    DemoPaginationIcons: typeof import('./src/views/demos/components/pagination/DemoPaginationIcons.vue')['default']
    DemoPaginationLength: typeof import('./src/views/demos/components/pagination/DemoPaginationLength.vue')['default']
    DemoPaginationOutline: typeof import('./src/views/demos/components/pagination/DemoPaginationOutline.vue')['default']
    DemoPaginationOutlineCircle: typeof import('./src/views/demos/components/pagination/DemoPaginationOutlineCircle.vue')['default']
    DemoPaginationSize: typeof import('./src/views/demos/components/pagination/DemoPaginationSize.vue')['default']
    DemoPaginationTotalVisible: typeof import('./src/views/demos/components/pagination/DemoPaginationTotalVisible.vue')['default']
    DemoProgressCircularColor: typeof import('./src/views/demos/components/progress-circular/DemoProgressCircularColor.vue')['default']
    DemoProgressCircularIndeterminate: typeof import('./src/views/demos/components/progress-circular/DemoProgressCircularIndeterminate.vue')['default']
    DemoProgressCircularRotate: typeof import('./src/views/demos/components/progress-circular/DemoProgressCircularRotate.vue')['default']
    DemoProgressCircularSize: typeof import('./src/views/demos/components/progress-circular/DemoProgressCircularSize.vue')['default']
    DemoProgressLinearBuffering: typeof import('./src/views/demos/components/progress-linear/DemoProgressLinearBuffering.vue')['default']
    DemoProgressLinearColor: typeof import('./src/views/demos/components/progress-linear/DemoProgressLinearColor.vue')['default']
    DemoProgressLinearIndeterminate: typeof import('./src/views/demos/components/progress-linear/DemoProgressLinearIndeterminate.vue')['default']
    DemoProgressLinearReversed: typeof import('./src/views/demos/components/progress-linear/DemoProgressLinearReversed.vue')['default']
    DemoProgressLinearRounded: typeof import('./src/views/demos/components/progress-linear/DemoProgressLinearRounded.vue')['default']
    DemoProgressLinearSlots: typeof import('./src/views/demos/components/progress-linear/DemoProgressLinearSlots.vue')['default']
    DemoProgressLinearStriped: typeof import('./src/views/demos/components/progress-linear/DemoProgressLinearStriped.vue')['default']
    DemoRadioBasic: typeof import('./src/views/demos/forms/form-elements/radio/DemoRadioBasic.vue')['default']
    DemoRadioColors: typeof import('./src/views/demos/forms/form-elements/radio/DemoRadioColors.vue')['default']
    DemoRadioDensity: typeof import('./src/views/demos/forms/form-elements/radio/DemoRadioDensity.vue')['default']
    DemoRadioIcon: typeof import('./src/views/demos/forms/form-elements/radio/DemoRadioIcon.vue')['default']
    DemoRadioInline: typeof import('./src/views/demos/forms/form-elements/radio/DemoRadioInline.vue')['default']
    DemoRadioLabelSlot: typeof import('./src/views/demos/forms/form-elements/radio/DemoRadioLabelSlot.vue')['default']
    DemoRadioValidation: typeof import('./src/views/demos/forms/form-elements/radio/DemoRadioValidation.vue')['default']
    DemoRangeSliderBasic: typeof import('./src/views/demos/forms/form-elements/range-slider/DemoRangeSliderBasic.vue')['default']
    DemoRangeSliderColor: typeof import('./src/views/demos/forms/form-elements/range-slider/DemoRangeSliderColor.vue')['default']
    DemoRangeSliderDisabled: typeof import('./src/views/demos/forms/form-elements/range-slider/DemoRangeSliderDisabled.vue')['default']
    DemoRangeSliderStep: typeof import('./src/views/demos/forms/form-elements/range-slider/DemoRangeSliderStep.vue')['default']
    DemoRangeSliderThumbLabel: typeof import('./src/views/demos/forms/form-elements/range-slider/DemoRangeSliderThumbLabel.vue')['default']
    DemoRangeSliderVertical: typeof import('./src/views/demos/forms/form-elements/range-slider/DemoRangeSliderVertical.vue')['default']
    DemoRatingBasic: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingBasic.vue')['default']
    DemoRatingClearable: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingClearable.vue')['default']
    DemoRatingColors: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingColors.vue')['default']
    DemoRatingDensity: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingDensity.vue')['default']
    DemoRatingHover: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingHover.vue')['default']
    DemoRatingIncremented: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingIncremented.vue')['default']
    DemoRatingItemSlot: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingItemSlot.vue')['default']
    DemoRatingLength: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingLength.vue')['default']
    DemoRatingReadonly: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingReadonly.vue')['default']
    DemoRatingSize: typeof import('./src/views/demos/forms/form-elements/rating/DemoRatingSize.vue')['default']
    DemoSelectBasic: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectBasic.vue')['default']
    DemoSelectChips: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectChips.vue')['default']
    DemoSelectCustomTextAndValue: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectCustomTextAndValue.vue')['default']
    DemoSelectDensity: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectDensity.vue')['default']
    DemoSelectIcons: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectIcons.vue')['default']
    DemoSelectMenuProps: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectMenuProps.vue')['default']
    DemoSelectMultiple: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectMultiple.vue')['default']
    DemoSelectSelectionSlot: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectSelectionSlot.vue')['default']
    DemoSelectVariant: typeof import('./src/views/demos/forms/form-elements/select/DemoSelectVariant.vue')['default']
    DemoSimpleTableBasic: typeof import('./src/views/demos/forms/tables/simple-table/DemoSimpleTableBasic.vue')['default']
    DemoSimpleTableDensity: typeof import('./src/views/demos/forms/tables/simple-table/DemoSimpleTableDensity.vue')['default']
    DemoSimpleTableFixedHeader: typeof import('./src/views/demos/forms/tables/simple-table/DemoSimpleTableFixedHeader.vue')['default']
    DemoSimpleTableHeight: typeof import('./src/views/demos/forms/tables/simple-table/DemoSimpleTableHeight.vue')['default']
    DemoSimpleTableTheme: typeof import('./src/views/demos/forms/tables/simple-table/DemoSimpleTableTheme.vue')['default']
    DemoSliderAppendAndPrepend: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderAppendAndPrepend.vue')['default']
    DemoSliderAppendTextField: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderAppendTextField.vue')['default']
    DemoSliderBasic: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderBasic.vue')['default']
    DemoSliderColors: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderColors.vue')['default']
    DemoSliderDisabledAndReadonly: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderDisabledAndReadonly.vue')['default']
    DemoSliderIcons: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderIcons.vue')['default']
    DemoSliderMinAndMax: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderMinAndMax.vue')['default']
    DemoSliderSize: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderSize.vue')['default']
    DemoSliderStep: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderStep.vue')['default']
    DemoSliderThumb: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderThumb.vue')['default']
    DemoSliderTicks: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderTicks.vue')['default']
    DemoSliderValidation: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderValidation.vue')['default']
    DemoSliderVertical: typeof import('./src/views/demos/forms/form-elements/slider/DemoSliderVertical.vue')['default']
    DemoSnackbarBasic: typeof import('./src/views/demos/components/snackbar/DemoSnackbarBasic.vue')['default']
    DemoSnackbarMultiLine: typeof import('./src/views/demos/components/snackbar/DemoSnackbarMultiLine.vue')['default']
    DemoSnackbarPosition: typeof import('./src/views/demos/components/snackbar/DemoSnackbarPosition.vue')['default']
    DemoSnackbarTimeout: typeof import('./src/views/demos/components/snackbar/DemoSnackbarTimeout.vue')['default']
    DemoSnackbarTransition: typeof import('./src/views/demos/components/snackbar/DemoSnackbarTransition.vue')['default']
    DemoSnackbarVariants: typeof import('./src/views/demos/components/snackbar/DemoSnackbarVariants.vue')['default']
    DemoSnackbarVertical: typeof import('./src/views/demos/components/snackbar/DemoSnackbarVertical.vue')['default']
    DemoSnackbarWithAction: typeof import('./src/views/demos/components/snackbar/DemoSnackbarWithAction.vue')['default']
    DemoSwiperAutoplay: typeof import('./src/views/demos/components/swiper/DemoSwiperAutoplay.vue')['default']
    DemoSwiperBasic: typeof import('./src/views/demos/components/swiper/DemoSwiperBasic.vue')['default']
    DemoSwiperCenteredSlidesOption1: typeof import('./src/views/demos/components/swiper/DemoSwiperCenteredSlidesOption1.vue')['default']
    DemoSwiperCenteredSlidesOption2: typeof import('./src/views/demos/components/swiper/DemoSwiperCenteredSlidesOption2.vue')['default']
    DemoSwiperCoverflowEffect: typeof import('./src/views/demos/components/swiper/DemoSwiperCoverflowEffect.vue')['default']
    DemoSwiperCubeEffect: typeof import('./src/views/demos/components/swiper/DemoSwiperCubeEffect.vue')['default']
    DemoSwiperFade: typeof import('./src/views/demos/components/swiper/DemoSwiperFade.vue')['default']
    DemoSwiperGallery: typeof import('./src/views/demos/components/swiper/DemoSwiperGallery.vue')['default']
    DemoSwiperGrid: typeof import('./src/views/demos/components/swiper/DemoSwiperGrid.vue')['default']
    DemoSwiperLazyLoading: typeof import('./src/views/demos/components/swiper/DemoSwiperLazyLoading.vue')['default']
    DemoSwiperMultipleSlidesPerView: typeof import('./src/views/demos/components/swiper/DemoSwiperMultipleSlidesPerView.vue')['default']
    DemoSwiperNavigation: typeof import('./src/views/demos/components/swiper/DemoSwiperNavigation.vue')['default']
    DemoSwiperPagination: typeof import('./src/views/demos/components/swiper/DemoSwiperPagination.vue')['default']
    DemoSwiperProgress: typeof import('./src/views/demos/components/swiper/DemoSwiperProgress.vue')['default']
    DemoSwiperResponsiveBreakpoints: typeof import('./src/views/demos/components/swiper/DemoSwiperResponsiveBreakpoints.vue')['default']
    DemoSwiperVirtualSlides: typeof import('./src/views/demos/components/swiper/DemoSwiperVirtualSlides.vue')['default']
    DemoSwitchBasic: typeof import('./src/views/demos/forms/form-elements/switch/DemoSwitchBasic.vue')['default']
    DemoSwitchColors: typeof import('./src/views/demos/forms/form-elements/switch/DemoSwitchColors.vue')['default']
    DemoSwitchInset: typeof import('./src/views/demos/forms/form-elements/switch/DemoSwitchInset.vue')['default']
    DemoSwitchLabelSlot: typeof import('./src/views/demos/forms/form-elements/switch/DemoSwitchLabelSlot.vue')['default']
    DemoSwitchModelAsArray: typeof import('./src/views/demos/forms/form-elements/switch/DemoSwitchModelAsArray.vue')['default']
    DemoSwitchStates: typeof import('./src/views/demos/forms/form-elements/switch/DemoSwitchStates.vue')['default']
    DemoSwitchTrueAndFalseValue: typeof import('./src/views/demos/forms/form-elements/switch/DemoSwitchTrueAndFalseValue.vue')['default']
    DemoTabsAlignment: typeof import('./src/views/demos/components/tabs/DemoTabsAlignment.vue')['default']
    DemoTabsBasic: typeof import('./src/views/demos/components/tabs/DemoTabsBasic.vue')['default']
    DemoTabsBasicPill: typeof import('./src/views/demos/components/tabs/DemoTabsBasicPill.vue')['default']
    DemoTabsCustomIcons: typeof import('./src/views/demos/components/tabs/DemoTabsCustomIcons.vue')['default']
    DemoTabsDynamic: typeof import('./src/views/demos/components/tabs/DemoTabsDynamic.vue')['default']
    DemoTabsFixed: typeof import('./src/views/demos/components/tabs/DemoTabsFixed.vue')['default']
    DemoTabsGrow: typeof import('./src/views/demos/components/tabs/DemoTabsGrow.vue')['default']
    DemoTabsPagination: typeof import('./src/views/demos/components/tabs/DemoTabsPagination.vue')['default']
    DemoTabsProgrammaticNavigation: typeof import('./src/views/demos/components/tabs/DemoTabsProgrammaticNavigation.vue')['default']
    DemoTabsStacked: typeof import('./src/views/demos/components/tabs/DemoTabsStacked.vue')['default']
    DemoTabsVertical: typeof import('./src/views/demos/components/tabs/DemoTabsVertical.vue')['default']
    DemoTabsVerticalPill: typeof import('./src/views/demos/components/tabs/DemoTabsVerticalPill.vue')['default']
    DemoTextareaAutoGrow: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaAutoGrow.vue')['default']
    DemoTextareaBasic: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaBasic.vue')['default']
    DemoTextareaBrowserAutocomplete: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaBrowserAutocomplete.vue')['default']
    DemoTextareaClearable: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaClearable.vue')['default']
    DemoTextareaCounter: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaCounter.vue')['default']
    DemoTextareaIcons: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaIcons.vue')['default']
    DemoTextareaNoResize: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaNoResize.vue')['default']
    DemoTextareaRows: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaRows.vue')['default']
    DemoTextareaStates: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaStates.vue')['default']
    DemoTextareaValidation: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaValidation.vue')['default']
    DemoTextareaVariant: typeof import('./src/views/demos/forms/form-elements/textarea/DemoTextareaVariant.vue')['default']
    DemoTextfieldBasic: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldBasic.vue')['default']
    DemoTextfieldClearable: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldClearable.vue')['default']
    DemoTextfieldCounter: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldCounter.vue')['default']
    DemoTextfieldCustomColors: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldCustomColors.vue')['default']
    DemoTextfieldDensity: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldDensity.vue')['default']
    DemoTextfieldIconEvents: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldIconEvents.vue')['default']
    DemoTextfieldIcons: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldIcons.vue')['default']
    DemoTextfieldIconSlots: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldIconSlots.vue')['default']
    DemoTextfieldLabelSlot: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldLabelSlot.vue')['default']
    DemoTextfieldPasswordInput: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldPasswordInput.vue')['default']
    DemoTextfieldPrefixesAndSuffixes: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldPrefixesAndSuffixes.vue')['default']
    DemoTextfieldSingleLine: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldSingleLine.vue')['default']
    DemoTextfieldState: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldState.vue')['default']
    DemoTextfieldValidation: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldValidation.vue')['default']
    DemoTextfieldVariant: typeof import('./src/views/demos/forms/form-elements/textfield/DemoTextfieldVariant.vue')['default']
    DemoTooltipDelayOnHover: typeof import('./src/views/demos/components/tooltip/DemoTooltipDelayOnHover.vue')['default']
    DemoTooltipEvents: typeof import('./src/views/demos/components/tooltip/DemoTooltipEvents.vue')['default']
    DemoTooltipLocation: typeof import('./src/views/demos/components/tooltip/DemoTooltipLocation.vue')['default']
    DemoTooltipTooltipOnVariousElements: typeof import('./src/views/demos/components/tooltip/DemoTooltipTooltipOnVariousElements.vue')['default']
    DemoTooltipTransition: typeof import('./src/views/demos/components/tooltip/DemoTooltipTransition.vue')['default']
    DemoTooltipVModelSupport: typeof import('./src/views/demos/components/tooltip/DemoTooltipVModelSupport.vue')['default']
    DialogCloseBtn: typeof import('./src/@core/components/DialogCloseBtn.vue')['default']
    DropZone: typeof import('./src/@core/components/DropZone.vue')['default']
    EditCertificationFieldDialog: typeof import('./src/components/EditCertificationFieldDialog.vue')['default']
    EditCorrectionsDialog: typeof import('./src/components/EditCorrectionsDialog.vue')['default']
    EditFieldDialog: typeof import('./src/components/EditFieldDialog.vue')['default']
    EditPensionsModal: typeof import('./src/components/participants/pensionCalculation/EditPensionsModal.vue')['default']
    EmploymentInfo: typeof import('./src/components/participants/basicInfo/EmploymentInfo.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./src/components/dialogs/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./src/components/ErrorHeader.vue')['default']
    ExPartners: typeof import('./src/components/participants/basicInfo/ExPartners.vue')['default']
    GenTable: typeof import('./src/components/change-proposal/genTable.vue')['default']
    I18n: typeof import('./src/@core/components/I18n.vue')['default']
    IndexationBegining: typeof import('./src/components/participants/pensionCalculation/IndexationBegining.vue')['default']
    IndexationPensionDataUI: typeof import('./src/components/IndexationPensionDataUI.vue')['default']
    ManageUsersTable: typeof import('./src/components/Users/<USER>')['default']
    MaritalStatus: typeof import('./src/components/participants/basicInfo/MaritalStatus.vue')['default']
    MoreBtn: typeof import('./src/@core/components/MoreBtn.vue')['default']
    Notifications: typeof import('./src/@core/components/Notifications.vue')['default']
    NotificationsTable: typeof import('./src/components/notifications/NotificationsTable.vue')['default']
    ParticipantChangeFilter: typeof import('./src/components/change-proposal/requested/ParticipantChangeFilter.vue')['default']
    ParticipantChangeHistoryTable: typeof import('./src/components/change-proposal/history/ParticipantChangeHistoryTable.vue')['default']
    ParticipantChangeTable: typeof import('./src/components/change-proposal/requested/ParticipantChangeTable.vue')['default']
    ParticipantInformation: typeof import('./src/components/participants/basicInfo/ParticipantInformation.vue')['default']
    ParticipantsCertificationTable: typeof import('./src/components/participants/ParticipantsCertificationTable.vue')['default']
    ParticipantsFilter: typeof import('./src/components/participants/ParticipantsFilter.vue')['default']
    ParticipantsTable: typeof import('./src/components/participants/ParticipantsTable.vue')['default']
    PartTimePercentage: typeof import('./src/components/participants/salaryPension/PartTimePercentage.vue')['default']
    PaymentProvidersDialog: typeof import('./src/components/dialogs/PaymentProvidersDialog.vue')['default']
    PensionBaseHeader: typeof import('./src/components/participants/pensionCalculation/PensionBaseHeader.vue')['default']
    PensionCalculation: typeof import('./src/components/participants/pensionCalculation/PensionCalculation.vue')['default']
    PensionCalculationBase: typeof import('./src/components/participants/pensionCalculation/PensionCalculationBase.vue')['default']
    PensionCode: typeof import('./src/components/participants/pensionInfo/PensionCode.vue')['default']
    PensionCodeDialog: typeof import('./src/components/participants/pensionInfo/PensionCodeDialog.vue')['default']
    PensionInfo: typeof import('./src/components/participants/basicInfo/PensionInfo.vue')['default']
    PensionParamChangeHistoryTable: typeof import('./src/components/change-proposal/history/PensionParamChangeHistoryTable.vue')['default']
    PensionParamChangeTable: typeof import('./src/components/change-proposal/requested/PensionParamChangeTable.vue')['default']
    PensionPrimaryCalc: typeof import('./src/components/participants/salaryPension/PensionPrimaryCalc.vue')['default']
    PensionsAccrual: typeof import('./src/components/participants/pensionCalculation/PensionsAccrual.vue')['default']
    PensionsAfterCorrections: typeof import('./src/components/participants/pensionCalculation/PensionsAfterCorrections.vue')['default']
    PensionsAsPerReferenceDate: typeof import('./src/components/participants/pensionCalculation/PensionsAsPerReferenceDate.vue')['default']
    PensionsCalcStrip: typeof import('./src/components/participants/pensionCalculation/PensionsCalcStrip.vue')['default']
    PensionsEndOfPreviousCalenderYear: typeof import('./src/components/participants/pensionCalculation/PensionsEndOfPreviousCalenderYear.vue')['default']
    PensionTableLayout: typeof import('./src/components/participants/pensionCalculation/PensionTableLayout.vue')['default']
    PensionTableRow: typeof import('./src/components/participants/pensionCalculation/PensionTableRow.vue')['default']
    PersonalDetails: typeof import('./src/components/change-proposal/requested/PersonalDetails.vue')['default']
    PriceDetails: typeof import('./src/components/change-proposal/requested/PriceDetails.vue')['default']
    PricingCard: typeof import('./src/components/dialogs/PricingCard.vue')['default']
    PricingPlanDialog: typeof import('./src/components/dialogs/PricingPlanDialog.vue')['default']
    ProductDescriptionEditor: typeof import('./src/@core/components/ProductDescriptionEditor.vue')['default']
    PropertyArea: typeof import('./src/components/change-proposal/requested/PropertyArea.vue')['default']
    PropertyDetails: typeof import('./src/components/change-proposal/requested/PropertyDetails.vue')['default']
    PropertyFeatures: typeof import('./src/components/change-proposal/requested/PropertyFeatures.vue')['default']
    ReferAndEarnDialog: typeof import('./src/components/dialogs/ReferAndEarnDialog.vue')['default']
    RejectChangeDialog: typeof import('./src/components/change-proposal/RejectChangeDialog.vue')['default']
    RevertApproveRejectCertFields: typeof import('./src/components/certified-data/RevertApproveRejectCertFields.vue')['default']
    RevertChangesDialog: typeof import('./src/components/dialogs/RevertChangesDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SalaryEntityDialog: typeof import('./src/components/participants/salaryPension/SalaryEntityDialog.vue')['default']
    SalaryEntries: typeof import('./src/components/participants/salaryPension/SalaryEntries.vue')['default']
    SalaryEntryDialog: typeof import('./src/components/participants/salaryPension/SalaryEntryDialog.vue')['default']
    SalaryEntryForm: typeof import('./src/components/participants/salaryPension/SalaryEntryForm.vue')['default']
    SalaryPension: typeof import('./src/components/participants/salaryPension/SalaryPension.vue')['default']
    SalaryPensionBase: typeof import('./src/components/participants/salaryPension/SalaryPensionBase.vue')['default']
    ScrollToTop: typeof import('./src/@core/components/ScrollToTop.vue')['default']
    ShareProjectDialog: typeof import('./src/components/dialogs/ShareProjectDialog.vue')['default']
    Shortcuts: typeof import('./src/@core/components/Shortcuts.vue')['default']
    SingleColumnTableLayout: typeof import('./src/components/certified-data/pensionCalculation/SingleColumnTableLayout.vue')['default']
    SingleColumnTableRow: typeof import('./src/components/certified-data/pensionCalculation/SingleColumnTableRow.vue')['default']
    TablePagination: typeof import('./src/@core/components/TablePagination.vue')['default']
    TempBasicInfo: typeof import('./src/components/participants/basicInfo/tempBasicInfo.vue')['default']
    TheCustomizer: typeof import('./src/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./src/@core/components/ThemeSwitcher.vue')['default']
    TimelineBasic: typeof import('./src/views/demos/components/timeline/TimelineBasic.vue')['default']
    TimelineOutlined: typeof import('./src/views/demos/components/timeline/TimelineOutlined.vue')['default']
    TimelineWithIcons: typeof import('./src/views/demos/components/timeline/TimelineWithIcons.vue')['default']
    TiptapEditor: typeof import('./src/@core/components/TiptapEditor.vue')['default']
    TwoFactorAuthDialog: typeof import('./src/components/dialogs/TwoFactorAuthDialog.vue')['default']
    UserDialog: typeof import('./src/components/Users/<USER>')['default']
    UserInfoEditDialog: typeof import('./src/components/dialogs/UserInfoEditDialog.vue')['default']
    UserUpgradePlanDialog: typeof import('./src/components/dialogs/UserUpgradePlanDialog.vue')['default']
    VueApexCharts: typeof import('vue3-apexcharts')['default']
    YearLimitedDatePicker: typeof import('./src/components/participants/pensionCalculation/YearLimitedDatePicker.vue')['default']
  }
}
