<script setup lang="ts">
  import ParticipantChangeHistoryTable from '@/components/change-proposal/history/ParticipantChangeHistoryTable.vue'
  import PensionParamChangeHistoryTable from '@/components/change-proposal/history/PensionParamChangeHistoryTable.vue'

  const propertyListingSteps = [
    {
      title: 'Participant',
      subtitle: 'History of participant changes',
      icon: 'tabler-users',
    },
    {
      title: 'Pension Parameters',
      subtitle: 'History of pension parameter changes',
      icon: 'tabler-refresh',
    },

  ]

  const currentStep = ref(0)

</script>

<template>
  <VCard>
    <VRow no-gutters>
        <VCardText>
          <AppStepper
            v-model:current-step="currentStep"
            :items="propertyListingSteps"
            icon-size="22"
            class="stepper-icon-step-bg"
          />
        </VCardText>

      <VCol
        cols="12"
      >
        <VCardText>
          <VWindow
            v-model="currentStep"
            class="disable-tab-transition"
            direction="horizontal"
          >
            <VWindowItem>
              <ParticipantChangeHistoryTable />
            </VWindowItem>

            <VWindowItem>
              <PensionParamChangeHistoryTable />
            </VWindowItem>

          </VWindow>


        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>
