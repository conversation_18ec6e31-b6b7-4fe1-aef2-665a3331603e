<script setup lang="ts">

  const propertyListingSteps = [
    {
      title: 'Participant Changes',
      subtitle: 'Pending changes requested',
      icon: 'tabler-users',
    },
    {
      title: 'Pension Parameter Changes',
      subtitle: 'Pending changes requested',
      icon: 'tabler-refresh',
    },
    {
      title: 'Certfied Participant Changes',
      subtitle: 'Pending certified data changes requested',
      icon: 'tabler-checklist',
    },
  ]

  const currentStep = ref(0)

</script>

<template>
  <VCard>
    <VRow no-gutters>

        <VCardText>
          <AppStepper
            v-model:current-step="currentStep"
            :items="propertyListingSteps"
            icon-size="22"
            class="stepper-icon-step-bg"
          />
        </VCardText>

      <VCol
        cols="12"
      >
        <VCardText>
          <VWindow
            v-model="currentStep"
          >
            <VWindowItem>
              <ParticipantChangeTable />
            </VWindowItem>

            <VWindowItem>
              <PensionParamChangeTable />
            </VWindowItem>
            <VWindowItem>
              <CertifiedParticipantChangeTable />
            </VWindowItem>

          </VWindow>
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>
