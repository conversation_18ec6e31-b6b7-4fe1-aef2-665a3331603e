<template>
  <div class="add-participant-container">
    <div class="header-container">
      <div>
        <h1 class="text-h4 font-weight-bold">Add New Participant</h1>
        <p class="text-subtitle-1 text-grey-darken-1">Create a new pension participant</p>
      </div>
    </div>

    <v-overlay
      :model-value="loading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      ></v-progress-circular>
    </v-overlay>

    <AddParticipantForm 
      @submit="handleSubmit" 
      @cancel="handleCancel"
      :loading="loading"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useParticipants } from '@/composables/participants/useParticipants'
import AddParticipantForm from '@/components/participants/AddParticipantForm.vue'
import { useAppStore } from '@/stores/app/appStore'

const router = useRouter()
const {
  state: { creatingParticipant, error },
  actions: { createParticipant }
} = useParticipants()
const appStore = useAppStore()
const loading = ref(false)

const handleSubmit = async (formData: any) => {
  loading.value = true
  try {
    await participantStore.createParticipant(formData)
    appStore.showSnack('Participant created successfully')
    router.push('/participants')
  } catch (error) {
    console.error('Error creating participant:', error)
    appStore.showSnack('Error creating participant')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  router.push('/participants')
}
</script>

<style scoped>
.add-participant-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.header-container {
  margin-bottom: 32px;
}
</style> 