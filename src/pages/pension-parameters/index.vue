<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue'
  import { useAppStore } from '@/stores/app/appStore'
  import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
  import { ChangeType } from '@/gql/graphql'

  const appStore = useAppStore();
  const editDialog = ref(false);
  const currentTab = ref(0);

  const {state:{ allPensionParameters: pensionParametersData, loadingPensionParameters}, actions: {refetchPensionParameters}} = usePensionParameters()

  const selectedField = ref({
    field: '',
    value: '',
    id: '',
    typename: '',
    year: ''
  });

  const parameterDefinitions = {
    accrualPercentage: {
      title: 'Accrual Percentage',
      description: 'The percentage of pensionable salary that is accrued as pension each year',
      formatter: (value: number) => `${(value * 100).toFixed(2)}%`
    },
    annualMultiplier: {
      title: 'Annual Multiplier',
      description: 'The factor used to calculate annual pensionable salary from monthly salary',
      formatter: (value: number) => value
    },
    offsetAmount: {
      title: 'Offset Amount',
      description: 'The amount deducted from the pensionable salary before calculating pension accrual',
      formatter: (value: number) => `Afl. ${value.toLocaleString()}`
    },
    partnersPensionPercentage: {
      title: 'Partners Pension Percentage',
      description: 'The percentage of the pension that goes to the partner in case of death',
      formatter: (value: number) => `${(value * 100).toFixed(2)}%`
    },
    retirementAge: {
      title: 'Retirement Age',
      description: 'The age at which the participant is eligible for full pension benefits',
      formatter: (value: number) => value
    },
    voluntaryContributionInterestRate: {
      title: 'Voluntary Contribution Interest Rate',
      description: 'The interest rate applied to voluntary contributions',
      formatter: (value: number) => `${(value * 100).toFixed(2)}%`
    }
  };

  // Create tabs configuration based on parameter definitions
  const tabs = Object.keys(parameterDefinitions).map(paramType => ({
    title: parameterDefinitions[paramType].title,
    subtitle: parameterDefinitions[paramType].description,
    icon: 'tabler-settings', // You can customize icons per tab if needed
    paramType: paramType
  }));

  const parameterTables = computed(() => {
    const tables = {};

    Object.keys(parameterDefinitions).forEach(paramType => {
      const paramInfo = parameterDefinitions[paramType];
      const tableItems = [];

      const sortedData = [...pensionParametersData.value].sort((a, b) =>
        parseInt(b.year) - parseInt(a.year)
      );

      sortedData.forEach(param => {
        const isEditable = param.pendingChanges?.includes(paramType) || false;

        tableItems.push({
          year: param.year,
          value: paramInfo.formatter(param[paramType]),
          rawValue: param[paramType],
          id: param.id,
          editable: !isEditable,
        });
      });

      tables[paramType] = {
        title: paramInfo.title,
        description: paramInfo.description,
        items: tableItems
      };
    });

    return tables;
  });

  // Define table headers
  const headers = [
    { title: 'YEAR', key: 'year', width: '200px' },
    { title: 'VALUE', key: 'value' },
    { title: '', key: 'actions', sortable: false, width: '100px' }
  ];

  const openEditDialog = (item, paramType) => {
    if (!item.editable) {
      appStore.showSnack('Sorry you cannot edit this field');
      return;
    }

    selectedField.value = {
      field: paramType,
      value: item.rawValue,
      id: item.id,
      typename: 'PensionParameters',
      year: item.year
    };

    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const updateField = (newValue) => {
    closeEditDialog();
  };

  onMounted(async () => {
    await refetchPensionParameters()
  });
</script>

<template>
  <VCard>
    <VRow no-gutters>
      <VCol
        cols="12"
        md="6"
      >
      <VCardText>
        <AppStepper
          v-model:current-step="currentTab"
          :items="tabs"
          icon-size="22"
          direction="vertical"
          class="stepper-icon-step-bg"
        />
      </VCardText>
      </VCol>

      <VCol cols="6">
        <VCardText>
          <VWindow
            v-model="currentTab"
            class="disable-tab-transition"
            direction="vertical"
          >
            <VWindowItem
              v-for="tab in tabs"
              :key="tab.paramType"
            >
              <div class="parameter-card">
                <p class="parameter-description">{{ tab.subtitle }}</p>
                <div class="parameter-table-section">
                  <VDataTable
                    :headers="headers"
                    :items="parameterTables[tab.paramType].items"
                    hide-default-footer
                    class="elevation-0"
                    density="compact"
                  >
                    <template #item.actions="{ item }">
                      <VBtn
                        icon
                        size="small"
                        variant="text"
                        color="primary"
                        @click="openEditDialog(item, tab.paramType)"
                      >
                        <VIcon
                          v-if="!item.editable"
                          size="16"
                          icon="tabler-alert-triangle"
                          class="edit-icon"
                          color="warning"
                        />
                        <VIcon
                          v-else
                          size="16"
                          icon="tabler-edit"
                          class="edit-icon"
                          color="primary"
                        />
                      </VBtn>
                    </template>
                  </VDataTable>
                </div>
              </div>
            </VWindowItem>
          </VWindow>
        </VCardText>
      </VCol>
    </VRow>

    <!-- Edit Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :type="ChangeType.Parameters"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="selectedField.year"
      @close="closeEditDialog"
      @update="updateField"
    />
  </VCard>
</template>

<style scoped>
  .parameter-card {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .parameter-description {
    margin: 0 0 16px 0;
    color: #666;
    font-size: 14px;
  }

  .parameter-table-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .edit-icon {
    margin-left: 4px;
  }
</style>