<script setup lang="ts">
  import PensionCalculationBase from '@/components/participants/pensionCalculation/PensionCalculationBase.vue'
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import { useCertifiedData } from '@/composables/certified-data'

  const pensionStore = usePensionStore()
  const { state: { participantCertifiedData, loadingParticipantCertifiedData } } = useCertifiedData()

  const initializePensionMiscData = () => {
    const certifiedDataYears = participantCertifiedData.value.map((entry: any) => entry.certificationYear)
    pensionStore.setCertifiedDataYears(certifiedDataYears)

    const currentYear = new Date().getFullYear()
    const rightColDate = new Date(currentYear, 11, 31)
    const middleColDate = new Date(currentYear - 1, 11, 31)
    const leftColDate = new Date(currentYear - 2, 11, 31).toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric'
    })

    pensionStore.setMiddleColumnYear(currentYear - 1)
    pensionStore.setRightColumnYear(currentYear)
    pensionStore.setLeftColumnYear(currentYear - 2)
    pensionStore.setRightColumnDate(rightColDate)
    pensionStore.setMiddleColumnDate(middleColDate)
    pensionStore.setLeftColumnDate(leftColDate)
  }

  watch(
    [() => participantCertifiedData.value, () => loadingParticipantCertifiedData.value],
    ([data, loading]) => {
      if (!loading && data.length > 0) {
        initializePensionMiscData()
      }
    },
    { immediate: true }
  )

</script>

<template>
  <BasicInformation :editable="false" />
  <CertifiedDataList />
</template>

<style scoped lang="scss">
</style>