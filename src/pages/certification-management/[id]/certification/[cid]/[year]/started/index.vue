<script setup lang="ts">
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import { onBeforeRouteLeave, useRoute } from 'vue-router'
  import { useCertifiedDataByYearAndYearBefore } from '@/composables/certified-data/useCertifiedDataByYearAndYearBefore'
  import { ref, computed, onMounted } from 'vue'
  import RevertChangesDialog from '@/components/dialogs/RevertChangesDialog.vue'

  const pensionStore = usePensionStore()
  const route = useRoute()

  const {
    state: {
      certifiedDataByYearAndYearBeforeData
    },
    actions: {
      fetchCertifiedDataByYearAndYearBefore
    }
  } = useCertifiedDataByYearAndYearBefore()

  const setOnGoingCertification = (status: boolean) => {
    pensionStore.setOngoingCertification(status)
  }

  const startCertifications = computed(() => pensionStore.onGoingCertification)
  const leftYear = computed(() => {
    const year = (route.params as { year: string }).year
    return parseInt(year) - 1
  })

  const rightYear = computed(() => {
    const year = (route.params as { year: string }).year
    return parseInt(year)
  })

  const leftColSize = computed(() => startCertifications.value ? 6 : 0)
  const rightColSize = computed(() => startCertifications.value ? 6 : 12)

  // Revert changes dialog
  const revertDialog = ref(false)

  const openRevertDialog = () => {
    revertDialog.value = true
  }

  onMounted(async () => {
    await fetchCertifiedDataByYearAndYearBefore(rightYear.value)
    // Set comparison view as default
    setOnGoingCertification(true)
  })

  onBeforeRouteLeave(() => {
    pensionStore.setOngoingCertification(false)
  })
</script>

<template>
  <CertificationActions class="mb-6" />
  <v-row class="year-comparison">
    <v-col :cols="leftColSize" v-if="startCertifications">
      <div class="year-label">Certified Data: {{ leftYear }}</div>
    </v-col>
    <v-col :cols="rightColSize">
      <div class="d-flex align-center justify-space-between">
        <div class="year-label">Pending Certification: {{ rightYear }}
          <v-btn size="small" class="mr-4" color="error" variant="outlined" prepend-icon="tabler-refresh-alert" @click="openRevertDialog">
            Revert
          </v-btn>
        </div>

      </div>
    </v-col>
  </v-row>

  <!-- Basic Information side by side -->
  <v-row>
    <v-col :cols="leftColSize" v-if="startCertifications" :tableView="true">
      <CertifiedBasicInformation :year="leftYear" :editable="false" />
    </v-col>
    <v-col :cols="rightColSize">
      <CertifiedBasicInformation :tableView="true" :editable="startCertifications" />
    </v-col>
  </v-row>

  <!-- Participant Information side by side -->
  <v-row>
    <v-col :cols="leftColSize" v-if="startCertifications">
      <CertifiedParticipantInformation :year="leftYear" :editable="false" />
    </v-col>
    <v-col :cols="rightColSize">
      <CertifiedParticipantInformation :editable="startCertifications" />
    </v-col>
  </v-row>

  <!-- Salary Pension Base side by side -->
  <v-row>
    <v-col :cols="leftColSize" v-if="startCertifications">
      <CertifiedSalaryPensionBase :year="leftYear" :editable="false" />
    </v-col>
    <v-col :cols="rightColSize">
      <CertifiedSalaryPensionBase :year="rightYear" />
    </v-col>
  </v-row>

  <!-- Pension Calculation Base side by side -->
  <v-row>
    <v-col :cols="leftColSize" v-if="startCertifications">
      <CertifiedPensionCalculationBase :year="leftYear" :editable="false" />
    </v-col>
    <v-col :cols="rightColSize">
      <CertifiedPensionCalculationBase :year="rightYear" />
    </v-col>
  </v-row>

  <!-- Revert Changes Dialog -->
  <RevertChangesDialog
    v-model="revertDialog"
    :certification-year="rightYear"
    @reverted="fetchCertifiedDataByYearAndYearBefore(rightYear)"
  />
</template>

<style scoped>
  .header-row {
    background-color: #1976d2; /* Primary color */
    color: white;
    padding: 16px;
    margin-bottom: 24px;
    border-radius: 4px;
  }

  .header-title {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .header-subtitle {
    font-size: 16px;
    opacity: 0.9;
  }

  .year-comparison {
    margin-bottom: 24px;
  }

  .year-label {
    font-size: 24px;
    font-weight: 500;
    padding-bottom: 8px;
    border-bottom: 1px solid #1976d2;
  }
</style>