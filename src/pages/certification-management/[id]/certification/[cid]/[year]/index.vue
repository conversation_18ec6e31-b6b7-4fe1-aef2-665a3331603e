<script setup lang="ts">
  import { computed } from 'vue'
  import { usePensionBase } from '@/composables/pension-base/usePensionBase'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const rightYear = computed(() => {
    return parseInt(route.params.year as string)
  })

  const { getCertificationStatus } = usePensionBase()


  const leftColSize = 12

  const certificationStatus = computed(() => {
    return getCertificationStatus(rightYear.value)
  })

  const isEditable = computed(() => {
    return certificationStatus.value === 'pending'
  })
 
</script>

<template>
  <CertificationActions class="mb-6" />

  <!-- Basic Information side by side -->
  <v-row>
    <v-col :cols="leftColSize" ><CertifiedBasicInformation :activeCertification="true" :year="rightYear" :editable="isEditable" /></v-col>
  </v-row>

  <!-- Participant Information side by side -->
  <v-row>
    <v-col :cols="leftColSize" ><CertifiedParticipantInformation :activeCertification="true" :year="rightYear" :editable="isEditable" /></v-col>
  </v-row>

  <!-- Salary Pension Base side by side -->
  <v-row>
    <v-col :cols="leftColSize" ><CertifiedSalaryPensionBase :year="rightYear" :editable="isEditable" /></v-col>
  </v-row>

  <!-- Pension Calculation Base side by side -->
  <v-row>
    <v-col :cols="leftColSize" ><CertifiedPensionCalculationBase :year="rightYear" :editable="isEditable" /></v-col>
  </v-row>
</template>
