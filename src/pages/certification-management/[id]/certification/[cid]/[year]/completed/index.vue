<script setup lang="ts">
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import { onBeforeRouteLeave } from 'vue-router'

  const pensionStore = usePensionStore()
  const route = useRoute()

  const rightYear = computed(() => {
    return parseInt(route.params.year as string)
  })

  onBeforeRouteLeave(() => {
    pensionStore.setOngoingCertification(false)
  })
</script>

<template>
  <CertificationActions class="mb-6" />
  <v-row class="year-comparison">
    <v-col cols="12">
      <div class="year-label">Pending Certification: {{ rightYear }}</div>
    </v-col>
  </v-row>

  <!-- Basic Information -->
  <v-row>
    <v-col cols="12"><CertifiedBasicInformation :editable="false" /></v-col>
  </v-row>

  <!-- Participant Information -->
  <v-row>
    <v-col cols="12"><CertifiedParticipantInformation :editable="false" /></v-col>
  </v-row>

  <!-- Salary Pension Base -->
  <v-row>
    <v-col cols="12"><CertifiedSalaryPensionBase :year="rightYear" :editable="false" /></v-col>
  </v-row>

  <!-- Pension Calculation Base -->
  <v-row>
    <v-col cols="12"><CertifiedPensionCalculationBase :year="rightYear" :editable="false" /></v-col>
  </v-row>
</template>

<style scoped>
  .header-row {
    background-color: #1976d2; /* Primary color */
    color: white;
    padding: 16px;
    margin-bottom: 24px;
    border-radius: 4px;
  }

  .header-title {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .header-subtitle {
    font-size: 16px;
    opacity: 0.9;
  }

  .year-comparison {
    margin-bottom: 24px;
  }

  .year-label {
    font-size: 24px;
    font-weight: 500;
    padding-bottom: 8px;
    border-bottom: 1px solid #1976d2;
  }
</style>