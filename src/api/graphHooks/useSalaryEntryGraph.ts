import { useMutation } from '@vue/apollo-composable'
import { CreateSalaryEntryInput, SalaryEntry, UpdateSalaryEntryInput } from '@/gql/graphql'
import { CREATE_SALARY_ENTRY, DELETE_SALARY_ENTRY, UPDATE_SALARY_ENTRY } from '../graphql/mutations/salaryEntryMutations'
import { GET_PARTICIPANT_BY_ID } from '../graphql/queries/participantQueries'

export function useSalaryEntryGraph() {
    // Create a new salary entry
    const { mutate: createSalaryEntry, loading: createLoading, error: createError } = useMutation(CREATE_SALARY_ENTRY, {
        refetchQueries: [{ query: GET_PARTICIPANT_BY_ID }]
    })

    // Update an existing salary entry
    const { mutate: updateSalaryEntry, loading: updateLoading, error: updateError } = useMutation(UPDATE_SALARY_ENTRY, {
        refetchQueries: [{ query: GET_PARTICIPANT_BY_ID }]
    })

    // Delete a salary entry
    const { mutate: deleteSalaryEntry, loading: deleteLoading, error: deleteError } = useMutation(DELETE_SALARY_ENTRY, {
        refetchQueries: [{ query: GET_PARTICIPANT_BY_ID }]
    })

    // Create a new salary entry with input data
    const createEntry = async (input: CreateSalaryEntryInput): Promise<SalaryEntry | null> => {
        try {
            const result = await createSalaryEntry({ createSalaryEntryInput: input })
            return result?.data?.createSalaryEntry || null
        } catch (error) {
            console.error('Error creating salary entry:', error)
            return null
        }
    }

    const updateEntry = async (input: UpdateSalaryEntryInput): Promise<SalaryEntry | null> => {
        try {
            const result = await updateSalaryEntry({ updateSalaryEntryInput: input })
            return result?.data?.updateSalaryEntry || null
        } catch (error) {
            console.error('Error updating salary entry:', error)
            return null
        }
    }

    const deleteEntry = async (id: string): Promise<boolean> => {
        try {
            const result = await deleteSalaryEntry({ id })
            return !!result?.data?.deleteSalaryEntry
        } catch (error) {
            console.error('Error deleting salary entry:', error)
            return false
        }
    }

    return {
        createEntry,
        updateEntry,
        deleteEntry,
        loading: {
            create: createLoading,
            update: updateLoading,
            delete: deleteLoading
        },
        error: {
            create: createError,
            update: updateError,
            delete: deleteError
        }
    }
} 