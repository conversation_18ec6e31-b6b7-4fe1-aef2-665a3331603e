import { useQuery, provideApolloClient } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { GET_PENSION_PARAMETERS } from '@/api/graphql/queries/pensionParameterQueries'

export const usePensionParameterGraph = () => {
  provideApolloClient(apolloClient)

  const {
    result: pensionParameters,
    loading: loadingPensionParameters,
    refetch: refetchPensionParameters,
  } = useQuery(GET_PENSION_PARAMETERS)

  const allPensionParameters = computed(() =>
    pensionParameters.value?.getAllPensionParameters || []
  )


  return {
    state: {
      allPensionParameters,
      loadingPensionParameters,
    },
    actions: {
    refetchPensionParameters
    }
  }
}