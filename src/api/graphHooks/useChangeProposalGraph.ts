import { useQuery, useMutation, provideApolloClient } from '@vue/apollo-composable'

import {
  GET_PENSION_PARAMS_CHANGE_PROPOSALS,
  GET_PARTICIPANT_CHANGE_PROPOSALS,
  GET_PENSION_PARAMS_CHANGE_PROPOSALS_HISTORY,
  GET_PARTICIPANT_CHANGE_PROPOSALS_HISTORY
} from '@/api/graphql/queries/changeProposalQueries'

import {
  CREATE_CHANGE_PROPOSAL,
  UPDATE_CHANGE_PROPOSAL,
  DELETE_CHANGE_PROPOSAL,
  APPROVE_CHANGE_PROPOSAL,
  REJECT_CHANGE_PROPOSAL,
  GET_LATEST_APPROVED_CHANGE
} from '@/api/graphql/mutations/changeProposalMutation'
import { apolloClient } from '@/api/middleware/apolloClient'
import { useAuthStore } from '@/stores/auth/authStore'
import { GET_ALL_PARTICIPANTS, GET_PARTICIPANT_BY_ID } from '@/api/graphql/queries/participantQueries'
import { GET_PENSION_PARAMETERS } from '@/api/graphql/queries/pensionParameterQueries'
import { GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE } from '@/api/graphql/queries/certifiedDataQueries'

export const useChangeProposalGraph = () => {
  provideApolloClient(apolloClient)
  const authStore = useAuthStore()
  const currentUserId = computed(() => authStore.userId)

  const reviewerId = computed(() => "be196c0f-ee7b-42a3-9163-885f649e65ef")

  const {
    result: pensionParamChangeProposals,
    loading: loadingPensionParamChangeProposals,
    refetch: refetchPensionParamsChangeProposals,
  } = useQuery(GET_PENSION_PARAMS_CHANGE_PROPOSALS,
    { reviewerId: reviewerId.value },
  )

  const allPensionParamChangeProposals = computed(() =>
    pensionParamChangeProposals.value?.getAllPensionParamChangeProposals || []
  )

  const {
    result: participantChangeProposals,
    loading: loadingParticipantsChangeProposals,
    refetch: refetchParticipantChangeProposals,
  } = useQuery(GET_PARTICIPANT_CHANGE_PROPOSALS,
    { reviewerId: reviewerId.value },
  )

  const allParticipantChangeProposals = computed(() =>
    participantChangeProposals.value?.getAllParticipantChangeProposals || []
  )

  const {
    result: participantChangeProposalsHistory,
    loading: loadingParticipantsChangeProposalsHistory,
    refetch: refetchParticipantChangeProposalsHistory,
  } = useQuery(GET_PARTICIPANT_CHANGE_PROPOSALS_HISTORY,
    { reviewerId: reviewerId.value },
  )

  const allParticipantChangeProposalsHistory = computed(() =>
    participantChangeProposalsHistory.value?.getAllParticipantChangeProposalsHistory || []
  )


  const {
    result: pensionParamChangeProposalsHistory,
    loading: loadingPensionParamChangeProposalsHistory,
    refetch: refetchPensionParamsChangeProposalsHistory,
  } = useQuery(GET_PENSION_PARAMS_CHANGE_PROPOSALS_HISTORY,
    { reviewerId: reviewerId.value },
  )

  const allPensionParamChangeProposalsHistory = computed(() =>
    pensionParamChangeProposalsHistory.value?.getAllPensionParamChangeProposalsHistory || []
  )

  // Mutations
  const {
    mutate: getLatestApprovedChangeMutation,
    loading: loadingLatestApprovedChange
  } = useMutation(GET_LATEST_APPROVED_CHANGE)

  const {
    mutate: createChangeProposalMutation,
    loading: loadingCreateChangeProposal
  } = useMutation(CREATE_CHANGE_PROPOSAL, {
    refetchQueries: [GET_PARTICIPANT_CHANGE_PROPOSALS, GET_PENSION_PARAMS_CHANGE_PROPOSALS, GET_PARTICIPANT_BY_ID, GET_PENSION_PARAMETERS, GET_CERTIFIED_DATA_BY_YEAR_AND_YEAR_BEFORE],
  })

  const {
    mutate: updateChangeProposalMutation,
    loading: loadingUpdateChangeProposal
  } = useMutation(UPDATE_CHANGE_PROPOSAL)

  const {
    mutate: deleteChangeProposalMutation,
    loading: loadingDeleteChangeProposal
  } = useMutation(DELETE_CHANGE_PROPOSAL)

  const {
    mutate: approveChangeProposalMutation,
    loading: loadingApproveChangeProposal
  } = useMutation(APPROVE_CHANGE_PROPOSAL, {
    refetchQueries: [GET_PARTICIPANT_CHANGE_PROPOSALS, GET_PENSION_PARAMS_CHANGE_PROPOSALS, GET_PARTICIPANT_CHANGE_PROPOSALS_HISTORY, GET_PENSION_PARAMS_CHANGE_PROPOSALS_HISTORY, GET_PENSION_PARAMETERS, GET_ALL_PARTICIPANTS],
  })

  const {
    mutate: rejectChangeProposalMutation,
    loading: loadingRejectChangeProposal
  } = useMutation(REJECT_CHANGE_PROPOSAL, {
    refetchQueries: [GET_PARTICIPANT_CHANGE_PROPOSALS, GET_PENSION_PARAMS_CHANGE_PROPOSALS, GET_PARTICIPANT_CHANGE_PROPOSALS_HISTORY, GET_PENSION_PARAMS_CHANGE_PROPOSALS_HISTORY, GET_PENSION_PARAMETERS],
  })

  const updateChangeProposal = async (input: any) => {
    try {
      const response = await updateChangeProposalMutation({
        variables: { input }
      })
      return response?.data.updateChangeProposal
    } catch (error) {
      console.error('Error updating change proposal:', error)
      throw error
    }
  }

  const deleteChangeProposal = async (id: string) => {
    try {
      const response = await deleteChangeProposalMutation({
        variables: { id }
      })
      return response?.data?.deleteChangeProposal
    } catch (error) {
      console.error('Error deleting change proposal:', error)
      throw error
    }
  }

  const approveChangeProposal = async (id: string, comments = '', propagateChange: boolean = false) => {
    try {
      const response = await approveChangeProposalMutation({
        changeProposalId: id,
        reviewerId: reviewerId.value,
        changePropagated: propagateChange
      })
      return response?.data.approveChangeProposal
    } catch (error) {
      console.error('Error approving change proposal:', error)
      throw error
    }
  }

  const rejectChangeProposal = async (id: string, comments = '') => {
    try {
      const response = await rejectChangeProposalMutation({
        changeProposalId: id,
        reviewerComments: comments,
        reviewerId: reviewerId.value,
      })
      console.log({ response });
      return response?.data.rejectChangeProposal
    } catch (error) {
      console.error('Error rejecting change proposal:', error)
      throw error
    }
  }

  const getLatestApprovedChange = async (entityType: string, path: string) => {
    try {
      const response = await getLatestApprovedChangeMutation({
        entityType,
        path
      })
      return response?.data.getLatestApprovedChange
    } catch (error) {
      console.error('Error fetching latest approved change:', error)
      throw error
    }
  }

  return {
    state: {
      allPensionParamChangeProposals,
      allParticipantChangeProposals,
      allPensionParamChangeProposalsHistory,
      allParticipantChangeProposalsHistory,
      loadingParticipantsChangeProposals,
      loadingPensionParamChangeProposals,
      loadingPensionParamChangeProposalsHistory,
      loadingParticipantsChangeProposalsHistory,
      loadingCreateChangeProposal,
      loadingUpdateChangeProposal,
      loadingDeleteChangeProposal,
      loadingApproveChangeProposal,
      loadingRejectChangeProposal,
      loadingLatestApprovedChange
    },
    actions: {
      createChangeProposalMutation,
      updateChangeProposal,
      deleteChangeProposal,
      approveChangeProposal,
      rejectChangeProposal,
      refetchPensionParamsChangeProposals,
      getLatestApprovedChange
    }
  }
}

