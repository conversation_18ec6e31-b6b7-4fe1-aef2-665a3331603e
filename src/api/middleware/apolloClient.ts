import {
  ApolloClient,
  InMemoryCache,
  createHttpLink,
  from,
  split,
} from '@apollo/client/core'
import { onError } from '@apollo/client/link/error'
import { setContext } from '@apollo/client/link/context'
import { GraphQLWsLink } from '@apollo/client/link/subscriptions'
import { getMainDefinition } from '@apollo/client/utilities'
import { createClient } from 'graphql-ws'

// import { DebugLink } from '@storipress/apollo-vue-devtool'
import { getIdToken } from '@/api/middleware/getToken'
import { DEV_API_URL, DEV_WS_URL } from '@/constants/app'

export function createApolloClient() {
  const authLink = setContext(async (request, { headers }) => {
    if (request.operationName !== 'passwordReset') {
      const token = await getIdToken()

      return {
        headers: {
          ...headers,
          authorization: token ? `Bearer ${token}` : '',
        },
      }
    }
    else {
      return {
        headers: {
          ...headers,
        },
      }
    }
  })

  const errorLink = onError(({ graphQLErrors, networkError }) => {
    if (graphQLErrors) {
      // log and handle graphql errors - send to sentry
      console.log({ gqlErrors: graphQLErrors })
    }
    if (networkError) {
      // log and handle network errors - send to sentry
      console.log({ networkError })
    }
  })

  const wsLink = () => {
    // const wsUrl = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_WS_URL : DEV_WS_URL
    return new GraphQLWsLink(
      createClient({
        url: DEV_WS_URL,
        connectionParams: async () => {
          const token = await getIdToken()

          return {
            token,
          }
        },
      }),
    )
  }

  const httpLink = createHttpLink({
    uri: DEV_API_URL,
  })

  // const debugLink = new DebugLink()

  const HttpAuthlink = from([errorLink, authLink, httpLink])

  // using the ability to split links, you can send data to each link
  // depending on what kind of operation is being sent
  const link = split(

    // split based on operation type
    ({ query }) => {
      const definition = getMainDefinition(query)

      return (
        definition.kind === 'OperationDefinition'
        && definition.operation === 'subscription'
      )
    },
    wsLink(),
    HttpAuthlink,
  )

  return new ApolloClient({
    link,
    connectToDevTools: true,
    cache: new InMemoryCache(),
  })
}

export const apolloClient = createApolloClient()
