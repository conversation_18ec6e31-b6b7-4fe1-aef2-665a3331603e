import gql from 'graphql-tag'

export const CREATE_SALARY_ENTRY = gql`
  mutation CreateSalaryEntry($createSalaryEntryInput: CreateSalaryEntryInput!) {
    createSalaryEntry(createSalaryEntryInput: $createSalaryEntryInput) {
      id
      year
      amount
      partTimePercentage
      employmentInfoId
    }
  }
`

export const UPDATE_SALARY_ENTRY = gql`
  mutation UpdateSalaryEntry($updateSalaryEntryInput: UpdateSalaryEntryInput!) {
    updateSalaryEntry(updateSalaryEntryInput: $updateSalaryEntryInput) {
      id
      year
      amount
      partTimePercentage
      employmentInfoId
    }
  }
`

export const DELETE_SALARY_ENTRY = gql`
  mutation DeleteSalaryEntry($id: String!) {
    deleteSalaryEntry(id: $id) {
      id
    }
  }
` 