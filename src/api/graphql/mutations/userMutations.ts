import gql from 'graphql-tag';

export const CREATE_USER = gql`
    mutation CreateUser($createUserInput: CreateUserInput!) {
        createUser(createUserInput: $createUserInput) {
            id
            firebaseUid
            email
            firstname
            lastname
            lastLogin
        }
    }
`;

export const UPDATE_USER = gql`
    mutation UpdateUser($updateUserInput: UpdateUserInput!) {
        updateUser(updateUserInput: $updateUserInput) {
            id
            email
            firstname
            lastname
            lastLogin
        }
    }
`;

export const DELETE_USER = gql`
    mutation DeleteUser($id: String!) {
        removeUser(id: $id) {
            id
        }
    }
`;