import gql from 'graphql-tag'

export const MARK_NOTIFICATION_AS_READ = gql`
    mutation MarkNotificationAsRead($id: String!) {
        markNotificationAsRead(id: $id) {
            id
            read
            readAt
        }
    }
`

export const MARK_ALL_NOTIFICATIONS_AS_READ = gql`
    mutation MarkAllNotificationsAsRead($recipientId: String!) {
        markAllNotificationsAsRead(recipientId: $recipientId) 
    }
`