import gql from 'graphql-tag';

export const GET_ALL_USERS = gql`
    query GetAllUsers {
        getAllUsers {
            id
            firebaseUid
            email
            firstname
            lastname
            lastLogin
            role {
                id
                name
            }
        }
    }
`;

export const GET_USER_BY_ID = gql`
    query GetUser($id: String!) {
        getUserById(id: $id) {
            id
            firebaseUid
            email
            firstname
            lastname
            lastLogin
        }
    }
`;

export const GET_ALL_ROLES = gql`
    query GetAllRoles {
        roles {
            id
            name
        }
    }
`;