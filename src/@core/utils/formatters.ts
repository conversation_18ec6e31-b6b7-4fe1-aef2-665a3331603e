import { isToday } from './helpers'

export const avatarText = (value: string) => {
  if (!value)
    return ''
  const nameArray = value.split(' ')

  return nameArray.map(word => word.charAt(0).toUpperCase()).join('')
}

// TODO: Try to implement this: https://twitter.com/fireship_dev/status/1565424801216311297
export const kFormatter = (num: number) => {
  const regex = /\B(?=(\d{3})+(?!\d))/g

  return Math.abs(num) > 9999 ? `${Math.sign(num) * +((Math.abs(num) / 1000).toFixed(1))}k` : Math.abs(num).toFixed(0).replace(regex, ',')
}

/**
 * Format and return date in Humanize format
 * Intl docs: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/format
 * Intl Constructor: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat
 * @param {string} value date to format
 * @param {Intl.DateTimeFormatOptions} formatting Intl object to format with
 */
export const formatDate = (value: string, formatting: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', year: 'numeric' }) => {
  if (!value)
    return value

  return new Intl.DateTimeFormat('en-US', formatting).format(new Date(value))
}

/**
 * Return short human friendly month representation of date
 * Can also convert date to only time if date is of today (Better UX)
 * @param {string} value date to format
 * @param {boolean} toTimeForCurrentDay Shall convert to time if day is today/current
 */
export const formatDateToMonthShort = (value: string, toTimeForCurrentDay = true) => {
  const date = new Date(value)
  let formatting: Record<string, string> = { month: 'short', day: 'numeric' }

  if (toTimeForCurrentDay && isToday(date))
    formatting = { hour: 'numeric', minute: 'numeric' }

  return new Intl.DateTimeFormat('en-US', formatting).format(new Date(value))
}

export const prefixWithPlus = (value: number) => value > 0 ? `+${value}` : value


export const formatDateToString = (date: Date, type = 'en') => {
  if (!date) return ''
  const [y, m, d] = new Date(date).toISOString().slice(0, 10).split('-')
  return type === 'en' ? `${y}-${m}-${d}` : `${y}.${m}.${d}`
}

export const convertFairDaysToString = (fairDays: any) => {
  return fairDays?.map((day: any) => ({
    day: day.day.toISOString().split('T')[0],
    startTime: day.startTime.toISOString().split('T')[1].slice(0, 5),
    endTime: day.endTime.toISOString().split('T')[1].slice(0, 5),
  }))
}

export const convertFairDaysToDateTime = (fairDays: any) => {
  return fairDays?.map((day: any) => ({
    day: new Date(`${day.day}T00:00:00.000Z`),
    startTime: new Date(`${day.day}T${day.startTime}:00.000Z`),
    endTime: new Date(`${day.day}T${day.endTime}:00.000Z`),
  }))
}

export const timeAgo = (input: string): string => {
  if (!input) return ''
  const inputDate = new Date(input)
  const now = Date.now()
  const diffInMilliseconds = now - inputDate.getTime()

  const seconds = Math.floor(diffInMilliseconds / 1000)
  if (seconds < 60) {
    return `vor ${seconds} Sek`
  }

  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) {
    return `vor ${minutes} Min`
  }

  const hours = Math.floor(minutes / 60)
  if (hours < 24) {
    return `vor ${hours} Stun`
  }

  const days = Math.floor(hours / 24)
  return `vor ${days} Tag`
}
