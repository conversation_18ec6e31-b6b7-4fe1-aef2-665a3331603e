<script setup lang="ts">
import { useTheme } from 'vuetify'
import ScrollToTop from '@core/components/ScrollToTop.vue'
import initCore from '@core/initCore'
import { initConfigStore, useConfigStore } from '@core/stores/config'
import { hexToRgb } from '@core/utils/colorConverter'
import { useAppStore} from "@/stores/app/appStore";
import {initAuthStore} from "@/stores/auth/authStore";
import { usePensionStore } from '@/stores/pension/pensionStore'
import useUsers from '@/composables/users/useUsers'

const { global } = useTheme()

initCore()
initConfigStore()
initAuthStore()

const configStore = useConfigStore()
const appStore = useAppStore()
const pensionStore = usePensionStore()
const userStore = useUsers()
const snackbar = computed(() => appStore.showSnackbar)
const appLoader = computed(() => appStore.showLoader)
const snackMsg = computed(() => appStore.snackMsg)
</script>

<template>
  <v-snackbar v-model="snackbar" location="top">
    {{ snackMsg }}
    <template v-slot:actions>
      <v-btn color="primary" variant="text" @click="snackbar = false">
        close
      </v-btn>
    </template>
  </v-snackbar>
  <v-progress-linear
      color="primary"
      v-if="appLoader"
      model-value="100"
      indeterminate
      rounded
  ></v-progress-linear>
  <VLocaleProvider :rtl="configStore.isAppRTL">
    <!-- ℹ️ This is required to set the background color of active nav link based on currently active global theme's primary -->
    <VApp class="mainFont" :style="`--v-global-theme-primary: ${hexToRgb(global.current.value.colors.primary)}`">
      <RouterView />

      <ScrollToTop />
    </VApp>
  </VLocaleProvider>
</template>

<style lang="scss">
  .mainFont {
    font-family: 'Oxygen', sans-serif;
  }
</style>
