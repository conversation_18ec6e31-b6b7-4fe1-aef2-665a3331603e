interface DateParts {
  year: number;
  month: number;
  day: number;
}

function formatFieldName(field: string) {
  return field
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .replace(/(^|\s)\w/g, match => match.toUpperCase());
}

export const transformObjectToArray = (obj: any) => {
  if (!obj || typeof obj !== 'object') return [];

  const typename = obj.__typename;
  const id = obj.id;
  const pendingChanges = obj.pendingChanges || [];

  return Object.entries(obj)
    .filter(([key]) => key !== '__typename' && key !== 'pendingChanges')
    .map(([field, value]) => ({
      typename,
      id,
      name: formatFieldName(field),
      field,
      value,
      disabled: pendingChanges.includes(field)
    }));
}

export const convertToSameType = (newValue: any, oldValue: any): any => {
  if (typeof oldValue === 'number') {
    return Number(newValue);
  } else if (typeof oldValue === 'boolean') {
    return newValue === 'true' || newValue === true;
  } else if (typeof oldValue === 'string') {
    return String(newValue);
  } else if (oldValue instanceof Date) {
    return new Date(newValue);
  } else {
    return newValue;
  }
}

export const formatCurrency = (amount: number | null): string => {
  if (amount === null) return 'Afl. 0,00';
  return `Afl. ${amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,').replace('.', ',')}`
}
export const formatPercentage = (value: number): string => {
  if (!value || isNaN(value)) return '0%';
  return `${(value * 100).toFixed(2)}%`;
}

export const splitDate = (dateInput: string | Date | null | undefined): DateParts => {
  // Handle null/undefined by returning current date
  if (!dateInput) {
    return getCurrentDateParts();
  }

  let dateObj: Date;

  // Handle Date objects directly
  if (dateInput instanceof Date) {
    if (isNaN(dateInput.getTime())) {
      return getCurrentDateParts();
    }
    dateObj = dateInput;
  }
  // Handle string inputs
  else {
    const dateStr = String(dateInput).trim();

    // Try ISO format (e.g., "2023-05-15" or "2023-05-15T12:00:00Z")
    const isoMatch = dateStr.match(/^(\d{4})-(\d{1,2})-(\d{1,2})/);
    if (isoMatch) {
      return {
        year: parseInt(isoMatch[1], 10),
        month: parseInt(isoMatch[2], 10),
        day: parseInt(isoMatch[3], 10)
      };
    }

    // Try to parse as date string
    dateObj = new Date(dateStr);
    if (isNaN(dateObj.getTime())) {
      return getCurrentDateParts();
    }
  }

  // Return parts from valid date
  return {
    year: dateObj.getFullYear(),
    month: dateObj.getMonth() + 1, // Months are 0-indexed
    day: dateObj.getDate()
  };
};

// Helper function to get current date parts
const getCurrentDateParts = (): DateParts => {
  const now = new Date();
  return {
    year: now.getFullYear(),
    month: now.getMonth() + 1,
    day: now.getDate()
  };
};

export const calculateRetirementDate = (birthDate: { day: number; month: number; year: number }, retirementAge: number) => {
  const date = new Date(birthDate.year, birthDate.month - 1, birthDate.day);
  date.setMonth(date.getMonth() + (retirementAge * 12));

  // If not born on the 1st, move to 1st of next month
  if (birthDate.day !== 1) {
    date.setMonth(date.getMonth() + 1);
    date.setDate(1);
  }

  return date;
};


// Function to check if a year is a leap year
export const isLeapYear = (year: number) => {
  return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
};

// Function to calculate the number of days between two dates in a 360-day year
export const days360 = (endYear: number, endMonth: number, endDay: number, startYear: number, startMonth: number, startDay: number) => {
  const startDay360 = startDay === 31 ? 30 : startDay;
  const endDay360 = (endMonth === 2 && endDay === 28 && !isLeapYear(endYear)) ? 30 : endDay;

  return (endYear - startYear) * 360 + (endMonth - startMonth) * 30 + (endDay360 - startDay360);
};

export const safeCalculate = (fn: Function, ...args: any[]) => {
  try {
    return fn(...args) || 0;
  } catch (error) {
    console.error('Calculation error:', error);
    return 0;
  }
};