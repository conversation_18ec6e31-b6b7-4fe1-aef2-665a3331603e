import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import { getStorage } from 'firebase/storage'
import { getFunctions } from 'firebase/functions'
import {
  APIKEY,
  APPID,
  AUTHDOMAIN,
  MEASUREMENTID,
  MESSAGINGSENDERID,
  PROJECTID,
  ST<PERSON><PERSON><PERSON><PERSON>UCKET,
} from '@/constants/app'

const firebaseConfig = {
  apiKey: APIKEY,
  authDomain: AUTHDOMAIN,
  projectId: PROJECTID,
  storageBucket: STORAGEBUCKET,
  messagingSenderId: MESSAGINGSENDERID,
  appId: APPID,
  measurementId: MEASUREMENTID,
}

const bucketUrl: string = 'gs://pensionadminspha.firebasestorage.app'

const app = initializeApp(firebaseConfig)

const db = getFirestore(app)

const auth = getAuth(app)

const storage = getStorage(app, bucketUrl)

const functions = getFunctions(app, 'europe-west1')

export { app, db, auth, storage, functions }
