import { useAppStore } from '@/stores/app/appStore'
import { useUserGraph } from '@/api/graphHooks/useUserGraph'
import {useUserStore} from "@/stores/users/userStore";
import _ from 'lodash'

export const useUsers = () => {
  const appStore = useAppStore()

  const {
    state: {
      userList,
      rolesList,
      loadingUsers,
      loadingCreate,
      loadingUpdate,
      loadingDelete,
    },
    actions: {
      createUserMutation,
      updateUserMutation,
      deleteUserMutation,
    },
  } = useUserGraph()

  const userStore = useUserStore()

  const userData = computed(()=>{
      return userStore.userFormDetails
  })

  const handleCreateUser = async () => {
    try {
      await createUserMutation({createUserInput: userData.value})
      appStore.showSnack('User created successfully')
    }
    catch (error) {
      console.error('Error creating user:', error)
      appStore.showSnack('Error creating user')
    }
  }

  const handleUpdateUser = async () => {

    try {
      await updateUserMutation({ updateUserInput: _.omit(userData.value, ['email']) })
      userStore.hideUserFormDialog()
      appStore.showSnack('User updated successfully')

    }

    catch (error) {
      console.error('Error updating user:', error)
      appStore.showSnack('Error updating user')
    }

  }

  const handleDeleteUser = async () => {
    try {
      const id = userData.value.id

      await deleteUserMutation({ id })
      appStore.showSnack('User deleted successfully')
    }
    catch (error) {
      console.error('Error deleting user:', error)
      appStore.showSnack('Error deleting user')
    }
  }

  return {
    state: {
      userList,
      rolesList,
      loadingUsers,
      loadingCreate,
      loadingUpdate,
      loadingDelete,
    },
    actions: {
      handleCreateUser,
      handleUpdateUser,
      handleDeleteUser,
    },
  }
}

export default useUsers
