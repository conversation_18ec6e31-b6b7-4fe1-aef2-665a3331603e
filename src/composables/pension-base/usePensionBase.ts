import { usePensionStore } from '@/stores/pension/pensionStore'
import { useCertifiedData } from '@/composables/certified-data'
import { ref, computed } from 'vue'

export const usePensionBase = () => {
  const pensionStore = usePensionStore()
  const { state: { normalizedParticipantCertifiedData } } = useCertifiedData()

  /**
   * Gets the certification status for a year
   * @param year The year to check
   * @returns The certification status ('completed', 'started', 'pending', or null if no data)
   */
  const getCertificationStatus = (year: number | null) => {
    if (!year || !normalizedParticipantCertifiedData.value) return null

    // Get the certified data for the specified year
    const certifiedData = normalizedParticipantCertifiedData.value[year]

    // Return the certification status if it exists, otherwise null
    return certifiedData ? certifiedData.certificationStatus : 'pending'
  }

  /**
   * Checks if a year has certified data with 'completed' status for the current participant
   * @param year The year to check
   * @returns True if the year has certified data with 'completed' status, false otherwise
   */
  const hasCertifiedData = (year: number | null) => {
    const status = getCertificationStatus(year)
    return status === 'completed'
  }

  /**
   * Checks if a date is December 31st
   * @param date The date to check (can be a Date object or a string)
   * @returns True if the date is December 31st, false otherwise
   */
  const isDecember31st = (date: Date | string | null) => {
    if (!date) return false

    // Handle string date format (e.g., "December 31, 2024" or "Dec 31, 2024")
    if (typeof date === 'string') {
      // Check if the string contains "Dec" or "December" and "31"
      return (date.includes('Dec 31,') || date.includes('December 31,'))
    }

    // Handle Date object
    if (date instanceof Date) {
      return date.getMonth() === 11 && date.getDate() === 31
    }

    return false
  }

  /**
   * Gets the certification status text for display
   * @param year The year to check
   * @returns The certification status text for display
   */
  const getCertificationStatusText = (year: number | null) => {
    const status = getCertificationStatus(year)

    switch (status) {
      case 'completed':
        return 'Certified'
      case 'started':
        return 'Certification started'
      case 'pending':
        return 'Certification pending'
      default:
        return 'Certification pending'
    }
  }

  /**
   * Checks if a year and date combination should show as certified
   * @param year The year to check
   * @param date The date to check
   * @returns True if the year has certified data with 'completed' status and the date is December 31st, false otherwise
   */
  const shouldShowCertified = (year: number | null, date: Date | string | null) => {
    return hasCertifiedData(year) && isDecember31st(date)
  }

  /**
   * Creates a ref for expansion panel state
   * @param initialState Initial state of the expansion panel (default: true)
   * @returns Ref for expansion panel state
   */
  const createExpansionState = (initialState = true) => {
    return ref(initialState)
  }

  /**
   * Gets column years from pension store
   * @returns Object with leftColumnYear, middleColumnYear, and rightColumnYear
   */
  const getColumnYears = () => {
    const leftColumnYear = computed(() => pensionStore.leftColumnYear)
    const middleColumnYear = computed(() => pensionStore.middleColumnYear)
    const rightColumnYear = computed(() => pensionStore.rightColumnYear)

    return {
      leftColumnYear,
      middleColumnYear,
      rightColumnYear
    }
  }

  /**
   * Gets CSS class for status based on certification status
   * @param year The year to check
   * @returns CSS class string
   */
  const getStatusClass = (year: number | null) => {
    if (!year) return 'bg-orange-lighten-5 text-orange-darken-2'

    const status = getCertificationStatus(year)

    // Return appropriate class based on certification status
    switch (status) {
      case 'completed':
        return 'bg-green-lighten-5 text-green-darken-2'
      case 'started':
        return 'bg-yellow-lighten-5 text-amber-darken-2'
      case 'pending':
        return 'bg-orange-lighten-5 text-orange-darken-2'
      default:
        return 'bg-gray-lighten-5 text-gray-darken-2'
    }
  }

  /**
   * Formats a number as currency
   * @param amount The amount to format
   * @returns Formatted currency string
   */
  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined) return 'N/A'
    return new Intl.NumberFormat('nl-AW', {
      style: 'currency',
      currency: 'AWG',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  /**
   * Gets data for a specific column based on year and data type
   * @param year The year to get data for
   * @param dataType The type of data to retrieve (e.g., 'pensionInfo', 'certifiedPensionInfo', 'certifiedIndexationStartOfYear')
   * @returns The data for the specified year and type
   */
  const getColumnData = (year: number | null, dataType: string) => {
    if (!year) return null

    const participantData = computed(() => pensionStore.activeParticipant)

    if (hasCertifiedData(year)) {
      // For certified data, get from normalized certified data
      return normalizedParticipantCertifiedData.value?.[year]?.[dataType]
    } else {
      // For non-certified data, get from participant data
      return participantData.value?.pensionInfo
    }
  }

  return {
    hasCertifiedData,
    isDecember31st,
    shouldShowCertified,
    createExpansionState,
    getColumnYears,
    getStatusClass,
    formatCurrency,
    getColumnData,
    getCertificationStatus,
    getCertificationStatusText
  }
}
