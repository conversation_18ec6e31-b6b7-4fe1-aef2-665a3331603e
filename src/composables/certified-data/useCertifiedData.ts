import { computed } from 'vue'
import { useCertifiedDataGraph } from '@/api/graphHooks/useCertifiedDataGraph'
import { CertifiedData } from '@/gql/graphql'
import { transformObjectToArray } from '@/utils/transformers'
import { usePensionStore } from '@/stores/pension/pensionStore'

export const useCertifiedData = () => {
    const {
        state: {
            certifiedDataList,
            loadingCertifiedData,
            participantCertifiedData,
            loadingParticipantCertifiedData,
            latestCertification,
            loadingLatestCertification,
            loadingUpdateCertificationStatus
        },
        actions: {
            refetchCertifiedData,
            refetchParticipantCertifiedData,
            refetchLatestCertification,
            updateCertificationStatus
        }
    } = useCertifiedDataGraph()

    const pensionStore = usePensionStore()

    const certifiedDataYears = computed(()=> participantCertifiedData.value.map((entry: any) => entry.certificationYear))
    pensionStore.setCertifiedDataYears(certifiedDataYears.value);

    const formattedCertifiedData = computed(() => {
        return certifiedDataList.value.map((certifiedData: CertifiedData) => {
            return {
                id: certifiedData.id,
                participantId: certifiedData.participantId,
                certificationYear: certifiedData.certificationYear,
                certifiedAt: new Date(certifiedData.certifiedAt).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
                hasCertifiedEmploymentInfo: !!certifiedData.certifiedEmploymentInfo,
                hasCertifiedPensionInfo: !!certifiedData.certifiedPensionInfo,
                hasCertifiedPersonalInfo: !!certifiedData.certifiedPersonalInfo
            }
        })
    })

    const getLatestCertificationDetails = computed(() => {
        if (!latestCertification.value) return []

        return transformObjectToArray({
            certificationYear: latestCertification.value.certificationYear,
            certifiedAt: new Date(latestCertification.value.certifiedAt).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
            notes: latestCertification.value.notes || 'N/A'
        })
    })

    const getCertifiedEmploymentInfo = computed(() => {
        if (!latestCertification.value?.certifiedEmploymentInfo) return []

        const employmentInfo = latestCertification.value.certifiedEmploymentInfo

        return transformObjectToArray({
            employeeId: employmentInfo.employeeId || 'N/A',
            department: employmentInfo.department || 'N/A',
            position: employmentInfo.position || 'N/A',
            regNum: employmentInfo.regNum || 'N/A',
            havNum: employmentInfo.havNum || 'N/A',
            startDate: employmentInfo.startDate ? new Date(employmentInfo.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) : 'N/A',
            status: employmentInfo.status || 'N/A'
        })
    })

    const getCertifiedPersonalInfo = computed(() => {
        if (!latestCertification.value?.certifiedPersonalInfo) return []

        const personalInfo = latestCertification.value.certifiedPersonalInfo
        const birthYear = personalInfo.birthYear || '1900'
        const birthMonth = personalInfo.birthMonth || '01'
        const birthDay = personalInfo.birthDay || '01'
        const birthDate = new Date(`${birthYear}-${birthMonth}-${birthDay}`).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })

        return transformObjectToArray({
            firstName: personalInfo.firstName || 'N/A',
            lastName: personalInfo.lastName || 'N/A',
            birthDate: birthDate || 'N/A',
            email: personalInfo.email || 'N/A',
            phone: personalInfo.phone || 'N/A',
            maritalStatus: personalInfo.maritalStatus || 'N/A'
        })
    })

    const getCertifiedSalaryEntries = computed(() => {
        if (!latestCertification.value?.certifiedEmploymentInfo?.certifiedSalaryEntries?.length) return []

        return latestCertification.value.certifiedEmploymentInfo.certifiedSalaryEntries.map(entry => ({
            year: entry.year,
            partTimePercentage: `${(entry.partTimePercentage * 100).toFixed(0)}%`,
            amount: entry.amount
        }))
    })

    const normalizedCertifiedSalaryEntries = computed(() => {
        if (!latestCertification.value?.certifiedEmploymentInfo?.certifiedSalaryEntries?.length) return {}

        return latestCertification.value.certifiedEmploymentInfo.certifiedSalaryEntries.reduce((acc, entry) => {
            if (entry.year) {
                acc[entry.year] = {
                    year: entry.year,
                    partTimePercentage: `${(entry.partTimePercentage * 100).toFixed(0)}%`,
                    amount: entry.amount
                }
            }
            return acc
        }, {} as Record<number, any>)
    })

    const normalizedParticipantCertifiedData = computed(() => {
        if (!participantCertifiedData.value?.length) return {}

        const normalizedCertData = participantCertifiedData.value.reduce((acc, certData) => {
            if (certData.certificationYear) {
                acc[certData.certificationYear] = certData
            }
            return acc
        }, {} as Record<string, any>)

        pensionStore.setNormalizedCertifiedData(normalizedCertData)

        return normalizedCertData
    })

    return {
        state: {
            certifiedDataList: formattedCertifiedData,
            loadingCertifiedData,
            participantCertifiedData,
            normalizedParticipantCertifiedData,
            loadingParticipantCertifiedData,
            latestCertification,
            loadingLatestCertification,
            loadingUpdateCertificationStatus,
            latestCertificationDetails: getLatestCertificationDetails,
            certifiedEmploymentInfo: getCertifiedEmploymentInfo,
            certifiedPersonalInfo: getCertifiedPersonalInfo,
            certifiedSalaryEntries: getCertifiedSalaryEntries,
            normalizedCertifiedSalaryEntries
        },
        actions: {
            refetchCertifiedData,
            refetchParticipantCertifiedData,
            refetchLatestCertification,
            updateCertificationStatus
        }
    }
}