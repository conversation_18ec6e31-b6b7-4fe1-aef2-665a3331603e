import Swal from 'sweetalert2'

const confirmBtnClass =
  'v-btn v-btn--elevated bg-primary v-btn--density-default v-btn--size-default v-btn--variant-elevated mr-3 v-btn--color-primary'
const cancelBtnClass =
  'v-btn v-btn--elevated v-btn--density-default v-btn--size-default v-btn--variant-outlined v-btn--color-default'

export const confirmUserDeleteAlert = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Delete User?',
    text: 'Are you sure you want to delete this user?.',
    icon: 'warning',
    confirmButtonText: 'Yes, Delete User',
    cancelButtonText: 'Cancel',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmChangeApproval = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Approve this change?',
    text: 'Are you sure you want to approve this change?.',
    icon: 'warning',
    confirmButtonText: 'Yes, Approve Change',
    cancelButtonText: 'Cancel',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const passwordResetSuccess = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Password-Reset success!',
    text: 'Please log in with your new password.',
    icon: 'success',
    confirmButtonText: 'Ok',
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

