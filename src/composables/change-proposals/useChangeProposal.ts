import { useChangeProposalGraph } from '@/api/graphHooks/useChangeProposalGraph'
import { confirmChangeApproval } from '@/composables/useSweetAlert'
import { useAppStore } from '@/stores/app/appStore'
import { ChangeProposal, ChangeType } from '@/gql/graphql'
import { usePensionStore } from '@/stores/pension/pensionStore'

export interface ParticipantChange {
  id: string;
  key: string;
  participant: string;
  field: string;
  currentValue: string;
  newValue: string;
  effectiveDate: string;
  proposedBy: string;
  reviewedAt: string;
  reviewedBy: string;
  reviewComments: string;
  status: string;
  rawData: {
    proposal: ChangeProposal;
    change: {
      path: string;
      oldValue: any;
      newValue: any;
    };
  };
}

interface CreateChangeProposalInput {
  entityId: string;
  entityType: string;
  path: string;
  type: ChangeType;
  employmentInfoId?: string,
  newValue: string;
  oldValue: string;
  participantName?: string;
  effectiveDate: string;
  isCertificationProposal?: boolean;
}

export const useChangeProposal = () => {
  const isLoading = ref(false);
  const loadingFilteredProposals = ref(false);

  // Get graph hooks
  const {
    state: {
      allParticipantChangeProposals,
      allPensionParamChangeProposals,
      allParticipantChangeProposalsHistory,
      allPensionParamChangeProposalsHistory,
      loadingCreateChangeProposal,
      loadingUpdateChangeProposal,
      loadingApproveChangeProposal,
      loadingParticipantsChangeProposals,
      loadingPensionParamChangeProposals,
      loadingParticipantsChangeProposalsHistory,
      loadingPensionParamChangeProposalsHistory,
      loadingLatestApprovedChange
    },
    actions: {
      approveChangeProposal,
      rejectChangeProposal,
      createChangeProposalMutation,
      getLatestApprovedChange
    }
  } = useChangeProposalGraph();

  const appStore = useAppStore();
  const pensionStore = usePensionStore();

  const middleColYear = computed(() => pensionStore.middleColumnYear as number)

  const currentUserId = computed(() => "be196c0f-ee7b-42a3-9163-885f649e65ef") // TODO: Get from auth store once you can set in claims

  function transformChangeProposals(changeProposals: ChangeProposal[]): ParticipantChange[] {
    return changeProposals.flatMap((proposal) => {
      return proposal.changes.map((change, index) => {

        const effectiveDate = new Date(proposal.effectiveDate).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        });

        const formatValue = (value: any): string => {
          if (value === null || value === undefined) {
            return '-';
          }

          if (typeof value === 'object' && value !== null) {
            if (value.day && value.month && value.year) {
              // Format date object
              return new Date(value.year, value.month - 1, value.day).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              });
            }
            return '[object Object]';
          }
          return value.toString();
        };

        const participant = 'Alfredo Participant';

        const reviewedBy = proposal?.reviewedBy?.email;

        return {
          id: proposal.id,
          key: `${proposal.id}-${index}`, // Unique ID for each change
          participant: proposal?.participantName || participant,
          field: change.path,
          currentValue: formatValue(change.oldValue),
          newValue: formatValue(change.newValue),
          effectiveDate,
          reviewedBy,
          reviewedAt: proposal?.reviewedAt,
          reviewComments: proposal?.reviewComments,
          proposedBy: proposal.createdBy.email,
          status: proposal.status.toLowerCase(),
          rawData: { proposal, change } // Store raw data for potential use
        };
      });
    });
  }

  const participantChangeProposalsList = computed<ParticipantChange[]>(() => {
    const nonCertifiedProposals = allParticipantChangeProposals.value?.filter((proposal: any) => !proposal.isCertificationProposal)
    return transformChangeProposals(nonCertifiedProposals || []);
  });

  const participantCertifiedChangeProposalsList = computed<ParticipantChange[]>(() => {
    const certifiedProposals = allParticipantChangeProposals.value?.filter((proposal: any) => proposal.isCertificationProposal)
    return transformChangeProposals(certifiedProposals || []);
  });

  const pensionParamChangeProposalsList = computed<ParticipantChange[]>(() => {
    return transformChangeProposals(allPensionParamChangeProposals.value || []);
  });

  const pensionParamChangeProposalsHistoryList = computed<ParticipantChange[]>(() => {
    return transformChangeProposals(allPensionParamChangeProposalsHistory.value || []);
  });

  const participantChangeHistoryList = computed<ParticipantChange[]>(() => {
    return transformChangeProposals(allParticipantChangeProposalsHistory.value || []);
  });

  const handleApproveChangeProposal = async (id: string, propagateChange: boolean = false) => {
    isLoading.value = true;
    try {

       const approveRes =  await approveChangeProposal(
          id, '',
         propagateChange
        );
        appStore.showSnack("Change successfully approved.");

        pensionStore.updatePendingChanges(middleColYear.value, approveRes?.changes[0].path, approveRes?.entityType)
        pensionStore.setNewValue(middleColYear.value, approveRes?.changes[0].path, approveRes?.changes[0].newValue, approveRes?.entityType)
    } catch (error) {
      appStore.showSnack("Error approving change proposal.");
      console.error('Error approving change proposal:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const handleRejectChangeProposal = async (id: string, comments: string) => {
    isLoading.value = true;
    try {
      const rejectRes = await rejectChangeProposal(
         id, comments
      );

      pensionStore.updatePendingChanges(middleColYear.value, rejectRes?.changes[0].path, rejectRes?.entityType)

      appStore.showSnack("Change proposal rejected successfully.");

    } catch (error) {
      console.error('Error rejecting change proposal:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const createChangeProposal = async (changeInput: CreateChangeProposalInput ) => {

    const input = {
      createdById: currentUserId.value,
      entityId: changeInput.entityId,
      entityType: changeInput.entityType,
      effectiveDate: changeInput.effectiveDate,
      employmentInfoId: changeInput.employmentInfoId,
      participantName: changeInput.participantName,
      status: "PENDING",
      isCertificationProposal: changeInput.isCertificationProposal,
      type: changeInput.type || ChangeType.Participant,
    }

    const changeDataInput = {
      path: changeInput.path,
      newValue: changeInput.newValue.toString(),
      oldValue: changeInput.oldValue.toString(),
    };

    try {
      const response = await createChangeProposalMutation({
        input: {
            ...input,
          },
        changeDataInput: {...changeDataInput}
      }
      )
      return response?.data.createChangeProposal
    } catch (error) {
      console.error('Error creating change proposal:', error)
      throw error
    }
  }

  // Function to fetch the latest approved change for a given entity type and path
  const handleGetLatestApprovedChange = async (entityType: string, path: string) => {
    try {
      console.log('Fetching latest approved change for', entityType, 'and path', path)
      const response = await getLatestApprovedChange(entityType, path)
      return response
    } catch (error) {
      console.error('Error fetching latest approved change:', error)
      appStore.showSnack("Error fetching latest approved change.")
      throw error
    }
  }

  return {
    state: {
      pensionParamChangeProposalsList,
      participantChangeProposalsList,
      pensionParamChangeProposalsHistoryList,
      allParticipantChangeProposalsHistory,
      allPensionParamChangeProposalsHistory,
      participantCertifiedChangeProposalsList,
      participantChangeHistoryList,
      allPensionParamChangeProposals,
      loadingFilteredProposals,
      loadingPensionParamChangeProposalsHistory,
      loadingParticipantsChangeProposalsHistory,
      loadingParticipantsChangeProposals,
      loadingPensionParamChangeProposals,
      loadingCreateChangeProposal,
      loadingUpdateChangeProposal,
      loadingApproveChangeProposal,
      loadingLatestApprovedChange,
      isLoading
    },
    actions: {
      handleApproveChangeProposal,
      handleRejectChangeProposal,
      createChangeProposal,
      handleGetLatestApprovedChange,
    }
  };
};

