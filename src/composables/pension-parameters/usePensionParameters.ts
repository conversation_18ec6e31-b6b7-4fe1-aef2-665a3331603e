import { transformObjectToArray } from '@/utils/transformers'
import { usePensionParameterGraph } from '@/api/graphHooks/usePensionParameterGraph'

export const usePensionParameters = () => {
  const {
    state: {
      loadingPensionParameters,
      allPensionParameters
    },
    actions: {
      refetchPensionParameters
    }
  } = usePensionParameterGraph()


  const pensionParamsTransformed = computed(() => {
      return allPensionParameters.value.map((param: any) => {
      return transformObjectToArray(param)
    })

  })

  const  normalizePensionParams = computed(() =>{
       return allPensionParameters.value.reduce((acc: Record<number, any>, param: any) => {
      const year = parseInt(param.year);
      acc[year] = param;
      return acc;
     }, {})
  })

  const availableStatuses = ['Active', 'Pending', 'Inactive']

  return {
    state: {
      pensionParamsTransformed,
      allPensionParameters,
      availableStatuses,
      normalizePensionParams,
      loadingPensionParameters,
    },
    actions:{
      refetchPensionParameters
    }
  }
}