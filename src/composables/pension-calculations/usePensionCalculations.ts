

export const usePensionCalculations = () => {

  /**
   * Calculates the OP-TE (Old-Age Pension) accrual to reference date
   *
   * @param pensionBase - The pension base amount
   * @param accrualPercentage - The accrual percentage (decimal, e.g., 0.0175 for 1.75%)
   * @param startDate - The employment start date {year, month, day}
   * @param referenceYear - The year for which to calculate the accrual
   * @param parttimePercentage - Optional parttime percentage (decimal, e.g., 0.8 for 80%)
   * @param referenceDate - Optional specific reference date, defaults to year-end
   * @returns The calculated pension accrual amount or null if required data is missing
   */
  const calculateOpTeAccrualToReferenceDate = (
    pensionBase: number | undefined,
    accrualPercentage: number | undefined,
    startDate: { year: number; month: number; day: number } | undefined,
    referenceYear: number,
    parttimePercentage?: number,
    referenceDate?: { year: number; month: number; day: number }
  ): number | null => {
    if (!pensionBase || !accrualPercentage || !startDate) {
      return null;
    }

    // Set default reference date to December 31st of reference year if not provided
    const endDate = referenceDate || { year: referenceYear, month: 12, day: 31 };

    // Period start date is either January 1st of reference year or employment start date, whichever is later
    const periodStartDate = new Date(referenceYear, 0, 1);
    const employmentStartDate = new Date(startDate.year, startDate.month - 1, startDate.day);
    const calculationStartDate = employmentStartDate < periodStartDate ? periodStartDate : employmentStartDate;

    // Period end date
    const calculationEndDate = new Date(endDate.year, endDate.month - 1, endDate.day);

    // Calculate accrual period using 30/360 convention
    const accrualPeriod = days360(
      calculationEndDate.getFullYear(),
      calculationEndDate.getMonth() + 1,
      calculationEndDate.getDate(),
      calculationStartDate.getFullYear(),
      calculationStartDate.getMonth() + 1,
      calculationStartDate.getDate()
    ) / 360;

    // Calculate pension accrual
    let pension = pensionBase * accrualPercentage * accrualPeriod;

    // Apply parttime percentage if provided
    if (parttimePercentage !== undefined) {
      pension *= parttimePercentage;
    }

    return pension;
  }

  /**
   * Calculates the WP-TE (Widows/Partners Pension) accrual to reference date
   *
   * @param pensionBase - The pension base amount
   * @param accrualPercentage - The accrual percentage (decimal, e.g., 0.0175 for 1.75%)
   * @param partnersPensionPercentage - The partners pension percentage (decimal, e.g., 0.7 for 70%)
   * @param startDate - The employment start date {year, month, day}
   * @param referenceYear - The year for which to calculate the accrual
   * @param parttimePercentage - Optional parttime percentage (decimal, e.g., 0.8 for 80%)
   * @param referenceDate - Optional specific reference date, defaults to year-end
   * @returns The calculated partners pension accrual amount or null if required data is missing
   */
  const calculateWpTeAccrualToReferenceDate = (
    pensionBase: number | undefined,
    accrualPercentage: number | undefined,
    partnersPensionPercentage: number | undefined,
    startDate: { year: number; month: number; day: number } | undefined,
    referenceYear: number,
    parttimePercentage?: number,
    referenceDate?: { year: number; month: number; day: number }
  ): number | null => {
    // Validate required inputs
    if (!pensionBase || !accrualPercentage || !startDate || !partnersPensionPercentage) {
      return null;
    }

    // Set default reference date to December 31st of reference year if not provided
    const endDate = referenceDate || { year: referenceYear, month: 12, day: 31 };

    // Period start date is either January 1st of reference year or employment start date, whichever is later
    const periodStartDate = new Date(referenceYear, 0, 1);
    const employmentStartDate = new Date(startDate.year, startDate.month - 1, startDate.day);
    const calculationStartDate = employmentStartDate < periodStartDate ? periodStartDate : employmentStartDate;

    // Period end date
    const calculationEndDate = new Date(endDate.year, endDate.month - 1, endDate.day);

    // Calculate accrual period using 30/360 convention
    const accrualPeriod = days360(
      calculationEndDate.getFullYear(),
      calculationEndDate.getMonth() + 1,
      calculationEndDate.getDate(),
      calculationStartDate.getFullYear(),
      calculationStartDate.getMonth() + 1,
      calculationStartDate.getDate()
    ) / 360;

    // Calculate base pension accrual
    let basePension = pensionBase * accrualPercentage * accrualPeriod;

    // Apply parttime percentage if provided
    if (parttimePercentage !== undefined) {
      basePension *= parttimePercentage;
    }

    // Apply partners pension percentage to get WP-TE amount
    return basePension * partnersPensionPercentage;
  }

  /**
   * Calculates the OP-TE (Old-Age Pension) accrual after reference date until retirement
   *
   * @param dateOfBirth - The participant's date of birth {year, month, day}
   * @param retirementAge - The retirement age in years (e.g., 67.5)
   * @param referenceDate - The reference date from which to calculate future accrual
   * @param projectedSalary - The projected salary for future accrual calculation
   * @param accrualPercentage - The accrual percentage (decimal, e.g., 0.0175 for 1.75%)
   * @param parttimePercentage - Optional parttime percentage (decimal, e.g., 0.8 for 80%)
   * @param pensionBase - Optional pension base if different from projected salary
   * @returns The calculated future pension accrual amount or null if required data is missing
   */
  const calculateOpTeAccrualAfterReferenceDate = (
    dateOfBirth: { year: number; month: number; day: number } | undefined,
    retirementAge: number | undefined,
    referenceDate: { year: number; month: number; day: number } | Date,
    accrualPercentage: number | undefined,
    parttimePercentage?: number,
    pensionBase?: number
  ): number | null => {
    // Validate required inputs
    if (!dateOfBirth || !retirementAge || !accrualPercentage) {
      return null;
    }

    // Convert reference date to Date object if it's not already
    const calculationStartDate = referenceDate instanceof Date
      ? referenceDate
      : new Date(referenceDate.year, referenceDate.month - 1, referenceDate.day);

    // Calculate retirement date based on date of birth and retirement age
    const calculationEndDate = calculateRetirementDateNew(dateOfBirth, retirementAge);

    // If retirement date is before or equal to reference date, no future accrual
    if (calculationEndDate <= calculationStartDate) {
      return 0;
    }

    // Calculate accrual period using 30/360 convention
    const accrualPeriod = days360(
      calculationEndDate.getFullYear(),
      calculationEndDate.getMonth() + 1,
      calculationEndDate.getDate(),
      calculationStartDate.getFullYear(),
      calculationStartDate.getMonth() + 1,
      calculationStartDate.getDate()
    ) / 360;

    // Use provided pension base or calculate from projected salary
    // Calculate future pension accrual
    let futurePension = pensionBase? pensionBase * accrualPercentage * accrualPeriod : 0;

    // Apply parttime percentage if provided
    if (parttimePercentage !== undefined) {
      futurePension *= parttimePercentage;
    }

    return futurePension;
  }

  /**
   * Calculates the retirement date based on date of birth and retirement age
   *
   * @param dateOfBirth - The participant's date of birth {year, month, day}
   * @param retirementAge - The retirement age in years (e.g., 67.5)
   * @returns The calculated retirement date as a Date object
   */
  const calculateRetirementDate = (
    dateOfBirth: { year: number; month: number; day: number },
    retirementAge: number
  ): Date => {
    // Extract whole years and fractional part
    const wholeYears = Math.floor(retirementAge);
    const fractionalYears = retirementAge - wholeYears;

    // Calculate months from fractional years (rounded to nearest month)
    const additionalMonths = Math.round(fractionalYears * 12);

    // Calculate retirement date
    return new Date(
      dateOfBirth.year + wholeYears,
      dateOfBirth.month - 1 + additionalMonths,
      dateOfBirth.day
    );
  }

  const calculateRetirementDateNew = (birthDate: { day: number; month: number; year: number }, retirementAge: number) => {
    const date = new Date(birthDate.year, birthDate.month - 1, birthDate.day);
    date.setMonth(date.getMonth() + (retirementAge * 12));

    // If not born on the 1st, move to 1st of next month
    if (birthDate.day !== 1) {
      date.setMonth(date.getMonth() + 1);
      date.setDate(1);
    }

    return date;
  };

  const calculateAccrualPeriodToReferenceDate = (
    startDate: { year: number; month: number; day: number } | undefined,
    referenceYear: number,
    referenceDate?: { year: number; month: number; day: number }
  ): number | null => {
    // Validate required inputs
    if ( !startDate ) {
      return null;
    }

    // Set default reference date to December 31st of reference year if not provided
    const endDate = referenceDate || { year: referenceYear, month: 12, day: 31 };

    // Period start date is either January 1st of reference year or employment start date, whichever is later
    const periodStartDate = new Date(referenceYear, 0, 1);
    const employmentStartDate = new Date(startDate.year, startDate.month - 1, startDate.day);
    const calculationStartDate = employmentStartDate < periodStartDate ? periodStartDate : employmentStartDate;

    // Period end date
    const calculationEndDate = new Date(endDate.year, endDate.month - 1, endDate.day);

    // Calculate accrual period using 30/360 convention
    const accrualPeriod = days360(
      calculationEndDate.getFullYear(),
      calculationEndDate.getMonth() + 1,
      calculationEndDate.getDate(),
      calculationStartDate.getFullYear(),
      calculationStartDate.getMonth() + 1,
      calculationStartDate.getDate()
    ) / 360;

    return accrualPeriod;
  }

  const calculateAccrualPeriodAfterReferenceDate = (
    dateOfBirth: { year: number; month: number; day: number } | undefined,
    retirementAge: number | undefined,
    referenceDate: { year: number; month: number; day: number } | Date,
  ): number | null => {
    // Validate required inputs
    if (!dateOfBirth || !retirementAge) {
      return null;
    }

    // Convert reference date to Date object if it's not already
    const calculationStartDate = referenceDate instanceof Date
      ? referenceDate
      : new Date(referenceDate.year, referenceDate.month - 1, referenceDate.day);

    // Calculate retirement date based on date of birth and retirement age
    const calculationEndDate = calculateRetirementDateNew(dateOfBirth, retirementAge);

    // If retirement date is before or equal to reference date, no future accrual
    if (calculationEndDate <= calculationStartDate) {
      return 0;
    }

    // Calculate accrual period using 30/360 convention
    const accrualPeriod = days360(
      calculationEndDate.getFullYear(),
      calculationEndDate.getMonth() + 1,
      calculationEndDate.getDate(),
      calculationStartDate.getFullYear(),
      calculationStartDate.getMonth() + 1,
      calculationStartDate.getDate()
    ) / 360;

    return accrualPeriod;
  }


  return {
    actions:{
      calculateRetirementDate,
      calculateAccrualPeriodToReferenceDate,
      calculateAccrualPeriodAfterReferenceDate,
      calculateOpTeAccrualToReferenceDate,
      calculateWpTeAccrualToReferenceDate,
      calculateOpTeAccrualAfterReferenceDate
    }
  }
}