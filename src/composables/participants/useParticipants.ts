import { useParticipantGraph } from '@/api/graphHooks/useParticipantGraph'
import { Participant, EmploymentInfo } from '@/types/participant.types'
import { SalaryEntry } from '@/gql/graphql'
import { transformObjectToArray } from '@/utils/transformers'
import { usePensionStore } from '@/stores/pension/pensionStore'

export const useParticipants = () => {
  const {
    state: {
      participantsList,
      loadingParticipants,
      singleParticipant,
      loadingParticipant
    },
    actions: {
      refetchParticipants,
      refetchSingleParticipant
    }
  } = useParticipantGraph()

  const pensionStore = usePensionStore()

  const formattedParticipants = computed(() => {
    return participantsList.value.map((participant: Participant) => {
      const { personalInfo } = participant
      const birthDate = new Date(personalInfo.birthYear, personalInfo.birthMonth - 1, personalInfo.birthDay).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
      const fullName = `${personalInfo.firstName} ${personalInfo.lastName}`
      return {
        lastName: participant?.personalInfo?.lastName,
        firstName: participant?.personalInfo?.firstName,
        id: participant.id,
        fullName,
        birthDate,
        status: participant?.pensionInfo?.codeDescription || participant?.status,
      }
    })
  })


  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success'
      case 'pending':
        return 'warning'
      case 'inactive':
        return 'error'
      default:
        return 'default'
    }
  }

  const participantDetails = computed(() => {
    pensionStore.setActiveParticipant(singleParticipant.value)
    const birthYear = singleParticipant.value?.personalInfo.birthYear || '1900'
    const birthMonth = singleParticipant.value?.personalInfo.birthMonth || '01'
    const birthDay = singleParticipant.value?.personalInfo.birthDay || '01'
    const birthDate = new Date(`${birthYear}-${birthMonth}-${birthDay}`).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })

    return transformObjectToArray({ ...singleParticipant.value?.personalInfo, birthDate: birthDate })
  })

  const participantPartnerInfo = computed(() => {
    const partnerInfo = singleParticipant.value?.personalInfo?.partnerInfo.filter((info: any) => info.isCurrent === true)
    if (!partnerInfo) return []
    return transformObjectToArray(partnerInfo[0] || {})
  })

  const participantExPartnerInfo = computed(() => {
    const partnerInfo = singleParticipant.value?.personalInfo?.partnerInfo.filter((info: any) => info.isCurrent === false)
    if (!partnerInfo) return []
    return partnerInfo.map((info: any) => {
      return transformObjectToArray(info)
    });
  })

  const participantChildrenInfo = computed(() => {
    const children = singleParticipant.value?.personalInfo?.children || [];
    return children.map((child: any) => transformObjectToArray(child));
  });

  const participantAddressInfo = computed(() => {
    const address = singleParticipant.value?.personalInfo?.address || {};
    return transformObjectToArray(address);
  });

  const participantEmploymentInfo = computed<EmploymentInfo>(() => {
    return singleParticipant.value?.employmentInfo || {} as EmploymentInfo;
  });

  //add for participant pensionInfo
  const participantPensionInfo = computed(() => {
    return singleParticipant.value?.pensionInfo || {};
  });

  const participantSalaryEntries = computed(() => {
    return singleParticipant.value?.employmentInfo?.salaryEntries || [];
  });

  const normalizedParticipantSalaryEntries = computed(() => {
    const salaryEntries = singleParticipant.value?.employmentInfo?.salaryEntries || [];
    const startDate = singleParticipant.value?.employmentInfo?.startDate;

    const result: Record<string, any> = {};
    salaryEntries.forEach((entry) => {
      if (entry.year) {
        result[entry.year.toString()] = {
          ...entry,
          startDate: startDate
        };
      }
    });
    return result;
  });

  const availableStatuses = ['Active', 'Pending', 'Inactive']

  const route = useRoute()

  const getYearInReview = (editable: boolean): number => {
    const paramsYear = computed(() => {
      return parseInt(route.params.year as string)
    })
    return editable ? paramsYear.value : paramsYear.value - 1
  }


  return {
    state: {
      participantAddressInfo,
      participantChildrenInfo,
      participantsList: formattedParticipants,
      loadingParticipants,
      availableStatuses,
      participantDetails,
      loadingParticipant,
      participantPartnerInfo,
      participantExPartnerInfo,
      participantSalaryEntries,
      normalizedParticipantSalaryEntries,
      participantPersonalInfo: singleParticipant.value?.personalInfo,
      participantEmploymentInfo,
      participantPensionInfo
    },
    actions: {
      refetchParticipants,
      refetchSingleParticipant,
      getStatusColor,
      getYearInReview
    }
  }
}