import { useNotificationsGraph } from '@/api/graphHooks/useNotificationsGraph'
import { formatDistance } from 'date-fns'
import { useAppStore } from '@/stores/app/appStore'

export const useNotifications = () => {
  const {
    state: {
      userNotifications,
      unreadCount,
      loadingUserNotifications,
      loadingMarkAsRead,
      loadingMarkAllAsRead
    },
    actions: {
      markNotificationAsRead,
      markAllNotificationsAsRead,
      refetchUserNotifications
    }
  } = useNotificationsGraph()

  const appStore = useAppStore()
  const selectedNotification = ref(null)

  const formattedNotifications = computed(() => {
    return userNotifications.value.map((notification: any) => {
      const createdAtDate = new Date(notification.createdAt)
      const readAtDate = notification.readAt ? new Date(notification.readAt) : null

      return {
        ...notification,
        formattedCreatedAt: formatDistance(createdAtDate, new Date(), { addSuffix: true }),
        formattedReadAt: readAtDate ? formatDistance(readAtDate, new Date(), { addSuffix: true }) : null,
        creatorName: notification.createdBy ?
          `${notification.createdBy.firstname} ${notification.createdBy.lastname}` : 'System',
        typeDisplayName: formatNotificationType(notification.type)
      }
    })
  })

  const formatNotificationType = (type: any) => {
    switch (type) {
      case 'APPOINTMENT':
        return 'Appointment'
      case 'MESSAGE':
        return 'Message'
      case 'SYSTEM':
        return 'System'
      case 'CERTIFICATION_REJECTION':
        return 'Certification Rejected'
      default:
        return type
    }
  }

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'APPOINTMENT':
        return 'tabler-calendar-clock'
      case 'MESSAGE':
        return 'tabler-email'
      case 'SYSTEM':
        return 'tabler-bell'
      case 'CERTIFICATION_REJECTION':
        return 'tabler-gavel'
      default:
        return 'tabler-bell'
    }
  }

  // Get notification color based on type
  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'APPOINTMENT':
        return 'primary'
      case 'MESSAGE':
        return 'info'
      case 'SYSTEM':
        return 'warning'
      case 'CERTIFICATION_REJECTION':
        return 'error'
      default:
        return 'default'
    }
  }

  // Business logic functions
  const handleMarkAsRead = async (id: string) => {
    try {
      const result = await markNotificationAsRead(id)
      return result
    } catch (error) {
      appStore.showSnack('Error marking notification as read')
      console.error('Error marking notification as read:', error)
      throw error
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      const result = await markAllNotificationsAsRead()
      appStore.showSnack('All notifications marked as read')
      return result
    } catch (error) {
      appStore.showSnack('Error marking all notifications as read')
      throw error
    }
  }

  const setSelectedNotification = async (notification: any) => {
    selectedNotification.value = notification
    if (notification && !notification.read) {
      await handleMarkAsRead(notification.id)
    }
  }

  const refreshNotifications = () => {
    return refetchUserNotifications()
  }

  return {
    state: {
      userNotifications,
      formattedNotifications,
      unreadCount,
      selectedNotification,
      loadingUserNotifications,
      loadingOperations: computed(() =>
        loadingMarkAsRead.value ||
        loadingMarkAllAsRead.value
      )
    },
    actions: {
      handleMarkAsRead,
      handleMarkAllAsRead,
      setSelectedNotification,
      getNotificationIcon,
      getNotificationColor,
      refreshNotifications
    }
  }
}