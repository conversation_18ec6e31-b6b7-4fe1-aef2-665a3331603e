import gql from 'graphql-tag';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
  JSON: { input: any; output: any; }
  JSONObject: { input: any; output: any; }
};

export type Address = {
  __typename?: 'Address';
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  houseNumber?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  personalInfo: PersonalInfo;
  postalCode?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  street?: Maybe<Scalars['String']['output']>;
};

export type AnnualAccrual = {
  __typename?: 'AnnualAccrual';
  employeeContributions?: Maybe<Scalars['Float']['output']>;
  employerContributions?: Maybe<Scalars['Float']['output']>;
  franchise?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  monthlyBenefit?: Maybe<Scalars['Float']['output']>;
  pensionData: PensionData;
};

export type AuditLog = {
  __typename?: 'AuditLog';
  action: Scalars['String']['output'];
  changes: Scalars['JSON']['output'];
  entityId: Scalars['String']['output'];
  entityType: Scalars['String']['output'];
  id: Scalars['String']['output'];
  ipAddress?: Maybe<Scalars['String']['output']>;
  proposalId?: Maybe<Scalars['String']['output']>;
  timestamp: Scalars['DateTime']['output'];
  user?: Maybe<User>;
  userAgent?: Maybe<Scalars['String']['output']>;
  userId: Scalars['String']['output'];
  userRole?: Maybe<Scalars['String']['output']>;
};

export type CertificationRejectReason = {
  __typename?: 'CertificationRejectReason';
  certifiedDataId?: Maybe<Scalars['String']['output']>;
  certifiedEmploymentInfoId?: Maybe<Scalars['String']['output']>;
  certifiedIndexationStartOfYearId?: Maybe<Scalars['String']['output']>;
  certifiedPensionCorrectionsId?: Maybe<Scalars['String']['output']>;
  certifiedPensionInfoId?: Maybe<Scalars['String']['output']>;
  certifiedPensionParametersId?: Maybe<Scalars['String']['output']>;
  certifiedPersonalInfoId?: Maybe<Scalars['String']['output']>;
  certifiedSalaryEntryId?: Maybe<Scalars['String']['output']>;
  certifiedVoluntaryContributionsId?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  field: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  reason: Scalars['String']['output'];
  status: CertificationRejectReasonStatus;
  updatedAt: Scalars['DateTime']['output'];
};

/** The status of a certification reject reason */
export enum CertificationRejectReasonStatus {
  Invalid = 'INVALID',
  Valid = 'VALID'
}

export type CertifiedAddress = {
  __typename?: 'CertifiedAddress';
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  houseNumber?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  personalInfo: CertifiedPersonalInfo;
  postalCode?: Maybe<Scalars['String']['output']>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
  state?: Maybe<Scalars['String']['output']>;
  street?: Maybe<Scalars['String']['output']>;
};

export type CertifiedChild = {
  __typename?: 'CertifiedChild';
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  isOrphan: Scalars['Boolean']['output'];
  isStudying: Scalars['Boolean']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  personalInfo?: Maybe<CertifiedPersonalInfo>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
};

export type CertifiedData = {
  __typename?: 'CertifiedData';
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certificationStatus?: Maybe<Scalars['String']['output']>;
  certificationYear: Scalars['Int']['output'];
  certifiedAddress?: Maybe<CertifiedAddress>;
  certifiedAt: Scalars['DateTime']['output'];
  certifiedBy: User;
  certifiedById: Scalars['String']['output'];
  certifiedChild?: Maybe<Array<CertifiedChild>>;
  certifiedEmploymentInfo?: Maybe<CertifiedEmploymentInfo>;
  certifiedIndexationStartOfYear?: Maybe<CertifiedIndexationStartOfYear>;
  certifiedPartnerInfo?: Maybe<Array<CertifiedPartnerInfo>>;
  certifiedPensionCorrections?: Maybe<CertifiedPensionCorrections>;
  certifiedPensionInfo?: Maybe<CertifiedPensionInfo>;
  certifiedPensionParameters?: Maybe<CertifiedPensionParameters>;
  certifiedPersonalInfo?: Maybe<CertifiedPersonalInfo>;
  certifiedVoluntaryContributions?: Maybe<CertifiedVoluntaryContributions>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['ID']['output'];
  notes?: Maybe<Scalars['String']['output']>;
  participant: Participant;
  participantId: Scalars['String']['output'];
};

export type CertifiedDataByYearResponse = {
  __typename?: 'CertifiedDataByYearResponse';
  /** Certifications grouped by year, with keys being the year strings */
  data: Scalars['JSONObject']['output'];
};

export type CertifiedEmploymentInfo = {
  __typename?: 'CertifiedEmploymentInfo';
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  certifiedSalaryEntries?: Maybe<Array<CertifiedSalaryEntry>>;
  department?: Maybe<Scalars['String']['output']>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  employeeId?: Maybe<Scalars['String']['output']>;
  havNum?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  position?: Maybe<Scalars['String']['output']>;
  regNum?: Maybe<Scalars['Int']['output']>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
  startDate?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type CertifiedIndexationStartOfYear = {
  __typename?: 'CertifiedIndexationStartOfYear';
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>;
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  differences?: Maybe<Array<Scalars['String']['output']>>;
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
};

export type CertifiedPartnerInfo = {
  __typename?: 'CertifiedPartnerInfo';
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedPersonalInfoId: Scalars['String']['output'];
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  isCurrent: Scalars['Boolean']['output'];
  isDeceased: Scalars['Boolean']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
  startDate?: Maybe<Scalars['DateTime']['output']>;
};

export type CertifiedPensionCorrections = {
  __typename?: 'CertifiedPensionCorrections';
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>;
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  attainableGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  correction?: Maybe<Scalars['Float']['output']>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
  year?: Maybe<Scalars['String']['output']>;
};

export type CertifiedPensionInfo = {
  __typename?: 'CertifiedPensionInfo';
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>;
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  attainableGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  code?: Maybe<Scalars['Int']['output']>;
  codeDescription?: Maybe<Scalars['String']['output']>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>;
  grossAnnualPension?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  pensionBase?: Maybe<Scalars['Float']['output']>;
  previousCode?: Maybe<Scalars['Int']['output']>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
};

export type CertifiedPensionParameters = {
  __typename?: 'CertifiedPensionParameters';
  accrualPercentage?: Maybe<Scalars['Float']['output']>;
  annualMultiplier?: Maybe<Scalars['Float']['output']>;
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  differences?: Maybe<Array<Scalars['String']['output']>>;
  effectiveDate?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  offsetAmount?: Maybe<Scalars['Float']['output']>;
  partnersPensionPercentage?: Maybe<Scalars['Float']['output']>;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
  retirementAge?: Maybe<Scalars['Int']['output']>;
  voluntaryContributionInterestRate?: Maybe<Scalars['Float']['output']>;
  year?: Maybe<Scalars['String']['output']>;
};

export type CertifiedPersonalInfo = {
  __typename?: 'CertifiedPersonalInfo';
  address?: Maybe<Array<CertifiedAddress>>;
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  birthDay?: Maybe<Scalars['Int']['output']>;
  birthMonth?: Maybe<Scalars['Int']['output']>;
  birthYear?: Maybe<Scalars['Int']['output']>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  children?: Maybe<Array<CertifiedChild>>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  maritalStatus?: Maybe<Scalars['String']['output']>;
  partnerInfo?: Maybe<Array<CertifiedPartnerInfo>>;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  phone?: Maybe<Scalars['String']['output']>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
};

export type CertifiedSalaryEntry = {
  __typename?: 'CertifiedSalaryEntry';
  amount: Scalars['Float']['output'];
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedEmploymentInfo: CertifiedEmploymentInfo;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['String']['output'];
  partTimePercentage: Scalars['Float']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
  year: Scalars['Float']['output'];
};

export type CertifiedVoluntaryContributions = {
  __typename?: 'CertifiedVoluntaryContributions';
  approvedChanges?: Maybe<Array<Scalars['String']['output']>>;
  certificationRejectReason?: Maybe<Array<CertificationRejectReason>>;
  certifiedData: CertifiedData;
  certifiedDataId: Scalars['String']['output'];
  contributions?: Maybe<Scalars['JSON']['output']>;
  differences?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['ID']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  requestedChanges?: Maybe<Array<Scalars['String']['output']>>;
};

export type ChangeData = {
  __typename?: 'ChangeData';
  changeProposal: ChangeProposal;
  id: Scalars['ID']['output'];
  newValue: Scalars['JSON']['output'];
  oldValue?: Maybe<Scalars['JSON']['output']>;
  path: Scalars['String']['output'];
};

export type ChangeProposal = {
  __typename?: 'ChangeProposal';
  changePropagated: Scalars['Boolean']['output'];
  changes: Array<ChangeData>;
  createdAt: Scalars['DateTime']['output'];
  createdBy: User;
  effectiveDate: Scalars['DateTime']['output'];
  entityId: Scalars['String']['output'];
  entityType: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isCertificationProposal?: Maybe<Scalars['Boolean']['output']>;
  participantName?: Maybe<Scalars['String']['output']>;
  reviewComments?: Maybe<Scalars['String']['output']>;
  reviewedAt?: Maybe<Scalars['DateTime']['output']>;
  reviewedBy?: Maybe<User>;
  status: ChangeStatus;
  updatedAt: Scalars['DateTime']['output'];
};

export enum ChangeStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export enum ChangeType {
  CertifiedData = 'CERTIFIED_DATA',
  Parameters = 'PARAMETERS',
  Participant = 'PARTICIPANT',
  Salary = 'SALARY'
}

export type Child = {
  __typename?: 'Child';
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isOrphan: Scalars['Boolean']['output'];
  isStudying: Scalars['Boolean']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  personalInfo: PersonalInfo;
};

export type ClaimsResponse = {
  __typename?: 'ClaimsResponse';
  claims?: Maybe<FirebaseUserDto>;
  error?: Maybe<ErrorType>;
};

export type ConversionDetail = {
  __typename?: 'ConversionDetail';
  conversionDate?: Maybe<Scalars['DateTime']['output']>;
  conversionRate?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  oldAgePensionIncrease?: Maybe<Scalars['Float']['output']>;
  partnerPensionIncrease?: Maybe<Scalars['Float']['output']>;
  pensionData: PensionData;
};

export type CreateAddressInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  houseNumber?: InputMaybe<Scalars['String']['input']>;
  personalInfoId: Scalars['String']['input'];
  postalCode?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  street?: InputMaybe<Scalars['String']['input']>;
};

export type CreateAnnualAccrualInput = {
  employeeContributions?: InputMaybe<Scalars['Float']['input']>;
  employerContributions?: InputMaybe<Scalars['Float']['input']>;
  franchise?: InputMaybe<Scalars['Float']['input']>;
  monthlyBenefit?: InputMaybe<Scalars['Float']['input']>;
  pensionDataId: Scalars['String']['input'];
};

export type CreateAuditLogInput = {
  action: Scalars['String']['input'];
  changes: Scalars['JSON']['input'];
  entityId: Scalars['String']['input'];
  entityType: Scalars['String']['input'];
  ipAddress?: InputMaybe<Scalars['String']['input']>;
  proposalId?: InputMaybe<Scalars['String']['input']>;
  userAgent?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
  userRole?: InputMaybe<Scalars['String']['input']>;
};

export type CreateCertificationRejectReasonInput = {
  certifiedDataId?: InputMaybe<Scalars['String']['input']>;
  certifiedEmploymentInfoId?: InputMaybe<Scalars['String']['input']>;
  certifiedIndexationStartOfYearId?: InputMaybe<Scalars['String']['input']>;
  certifiedPensionCorrectionsId?: InputMaybe<Scalars['String']['input']>;
  certifiedPensionInfoId?: InputMaybe<Scalars['String']['input']>;
  certifiedPensionParametersId?: InputMaybe<Scalars['String']['input']>;
  certifiedPersonalInfoId?: InputMaybe<Scalars['String']['input']>;
  certifiedSalaryEntryId?: InputMaybe<Scalars['String']['input']>;
  certifiedVoluntaryContributionsId?: InputMaybe<Scalars['String']['input']>;
  field: Scalars['String']['input'];
  reason: Scalars['String']['input'];
  status?: InputMaybe<CertificationRejectReasonStatus>;
};

export type CreateCertifiedDataInput = {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  certificationYear: Scalars['Int']['input'];
  certifiedAt: Scalars['DateTime']['input'];
  certifiedBy: UserCreateNestedOneWithoutCertifiedDataInput;
  certifiedEmploymentInfo?: InputMaybe<CreateCertifiedEmploymentInfoInput>;
  certifiedIndexationStartOfYear?: InputMaybe<CreateCertifiedIndexationStartOfYearInput>;
  certifiedPensionCorrections?: InputMaybe<CreateCertifiedPensionCorrectionsInput>;
  certifiedPensionInfo?: InputMaybe<CreateCertifiedPensionInfoInput>;
  certifiedPensionParameters?: InputMaybe<CreateCertifiedPensionParametersInput>;
  certifiedPersonalInfo?: InputMaybe<CreateCertifiedPersonalInfoInput>;
  certifiedVoluntaryContributions?: InputMaybe<CreateCertifiedVoluntaryContributionsInput>;
  id?: InputMaybe<Scalars['String']['input']>;
  notes?: InputMaybe<Scalars['String']['input']>;
  participant: ParticipantCreateNestedOneWithoutCertifiedDataInput;
};

export type CreateCertifiedEmploymentInfoInput = {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  department?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  havNum?: InputMaybe<Scalars['Int']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
  regNum?: InputMaybe<Scalars['Int']['input']>;
  salaryEntries?: InputMaybe<Scalars['JSON']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type CreateCertifiedIndexationStartOfYearInput = {
  accruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  accruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>;
  accruedGrossAnnualSinglesPension?: InputMaybe<Scalars['Float']['input']>;
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  extraAccruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  extraAccruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>;
  grossAnnualDisabilityPension?: InputMaybe<Scalars['Float']['input']>;
};

export type CreateCertifiedPensionCorrectionsInput = {
  accruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  accruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>;
  accruedGrossAnnualSinglesPension?: InputMaybe<Scalars['Float']['input']>;
  attainableGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  correction?: InputMaybe<Scalars['Float']['input']>;
  extraAccruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  extraAccruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>;
  grossAnnualDisabilityPension?: InputMaybe<Scalars['Float']['input']>;
  year?: InputMaybe<Scalars['String']['input']>;
};

export type CreateCertifiedPensionInfoInput = {
  accruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  accruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>;
  accruedGrossAnnualSinglesPension?: InputMaybe<Scalars['Float']['input']>;
  attainableGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  code?: InputMaybe<Scalars['Int']['input']>;
  codeDescription?: InputMaybe<Scalars['String']['input']>;
  extraAccruedGrossAnnualOldAgePension?: InputMaybe<Scalars['Float']['input']>;
  extraAccruedGrossAnnualPartnersPension?: InputMaybe<Scalars['Float']['input']>;
  grossAnnualDisabilityPension?: InputMaybe<Scalars['Float']['input']>;
  pensionBase?: InputMaybe<Scalars['Float']['input']>;
};

export type CreateCertifiedPensionParametersInput = {
  accrualPercentage?: InputMaybe<Scalars['Float']['input']>;
  annualMultiplier?: InputMaybe<Scalars['Float']['input']>;
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
  offsetAmount?: InputMaybe<Scalars['Float']['input']>;
  partnersPensionPercentage?: InputMaybe<Scalars['Float']['input']>;
  retirementAge?: InputMaybe<Scalars['Int']['input']>;
  voluntaryContributionInterestRate?: InputMaybe<Scalars['Float']['input']>;
  year?: InputMaybe<Scalars['String']['input']>;
};

export type CreateCertifiedPersonalInfoInput = {
  address?: InputMaybe<Scalars['JSON']['input']>;
  birthDay?: InputMaybe<Scalars['Int']['input']>;
  birthMonth?: InputMaybe<Scalars['Int']['input']>;
  birthYear?: InputMaybe<Scalars['Int']['input']>;
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  children?: InputMaybe<Scalars['JSON']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  maritalStatus?: InputMaybe<Scalars['String']['input']>;
  partnerInfo?: InputMaybe<Scalars['JSON']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type CreateCertifiedVoluntaryContributionsInput = {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  contributions?: InputMaybe<Scalars['JSON']['input']>;
};

export type CreateChangeDataInput = {
  changeProposalId?: InputMaybe<Scalars['String']['input']>;
  newValue: Scalars['String']['input'];
  oldValue?: InputMaybe<Scalars['String']['input']>;
  path: Scalars['String']['input'];
};

export type CreateChangeProposalInput = {
  changePropagated?: InputMaybe<Scalars['Boolean']['input']>;
  createdById: Scalars['String']['input'];
  effectiveDate: Scalars['DateTime']['input'];
  employmentInfoId?: InputMaybe<Scalars['String']['input']>;
  entityId: Scalars['String']['input'];
  entityType: Scalars['String']['input'];
  isCertificationProposal?: InputMaybe<Scalars['Boolean']['input']>;
  participantName?: InputMaybe<Scalars['String']['input']>;
  reviewComments?: InputMaybe<Scalars['String']['input']>;
  reviewedById?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<ChangeStatus>;
  type?: InputMaybe<ChangeType>;
};

export type CreateChildInput = {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  isOrphan?: InputMaybe<Scalars['Boolean']['input']>;
  isStudying?: InputMaybe<Scalars['Boolean']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  personalInfoId: Scalars['String']['input'];
};

export type CreateConversionDetailsInput = {
  conversionDate?: InputMaybe<Scalars['DateTime']['input']>;
  conversionRate?: InputMaybe<Scalars['Float']['input']>;
  oldAgePensionIncrease?: InputMaybe<Scalars['Float']['input']>;
  partnerPensionIncrease?: InputMaybe<Scalars['Float']['input']>;
  pensionDataId: Scalars['String']['input'];
};

export type CreateDocumentInput = {
  createdBy?: InputMaybe<Scalars['String']['input']>;
  documentId?: InputMaybe<Scalars['String']['input']>;
  mimeType?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  participantId: Scalars['String']['input'];
  path?: InputMaybe<Scalars['String']['input']>;
  size?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  uploadedAt?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CreateEmploymentInfoInput = {
  department?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  havNum?: InputMaybe<Scalars['Float']['input']>;
  participantId: Scalars['String']['input'];
  position?: InputMaybe<Scalars['String']['input']>;
  regNum?: InputMaybe<Scalars['Float']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type CreateNotificationInput = {
  entityId: Scalars['String']['input'];
  entityType: Scalars['String']['input'];
  message: Scalars['String']['input'];
  recipientId: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type CreateParticipantInput = {
  createdBy: Scalars['String']['input'];
  lastModified?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  updatedBy: Scalars['String']['input'];
};

export type CreatePartnerInfoInput = {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  isCurrent: Scalars['Boolean']['input'];
  isDeceased: Scalars['Boolean']['input'];
  lastName?: InputMaybe<Scalars['String']['input']>;
  personalInfoId: Scalars['String']['input'];
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CreatePensionCorrectionsInput = {
  correction: Scalars['Float']['input'];
  createdById: Scalars['ID']['input'];
  reviewedAt: Scalars['DateTime']['input'];
  reviewedById: Scalars['ID']['input'];
  year: Scalars['String']['input'];
};

export type CreatePensionDataInput = {
  participantId: Scalars['String']['input'];
  pensionableAmount?: InputMaybe<Scalars['Float']['input']>;
  retirementDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  totalContributions?: InputMaybe<Scalars['Float']['input']>;
};

export type CreatePensionInfoInput = {
  code?: InputMaybe<Scalars['Int']['input']>;
  codeDescription?: InputMaybe<Scalars['String']['input']>;
  codeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
  codeImpact?: InputMaybe<Scalars['String']['input']>;
  participantId: Scalars['ID']['input'];
  previousCode?: InputMaybe<Scalars['Int']['input']>;
  previousCodeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CreatePensionParametersInput = {
  accrualPercentage: Scalars['Float']['input'];
  annualMultiplier: Scalars['Float']['input'];
  effectiveDate: Scalars['DateTime']['input'];
  offsetAmount: Scalars['Float']['input'];
  partnersPensionPercentage: Scalars['Float']['input'];
  pensionDataId: Scalars['String']['input'];
  retirementAge: Scalars['Int']['input'];
  userId: Scalars['ID']['input'];
  voluntaryContributionInterestRate: Scalars['Float']['input'];
  year: Scalars['String']['input'];
};

export type CreatePersonalInfoInput = {
  birthDay?: InputMaybe<Scalars['Int']['input']>;
  birthMonth?: InputMaybe<Scalars['Int']['input']>;
  birthYear?: InputMaybe<Scalars['Int']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  maritalStatus?: InputMaybe<Scalars['String']['input']>;
  participantId: Scalars['String']['input'];
  pendingChanges?: InputMaybe<Array<Scalars['String']['input']>>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type CreateRoleInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type CreateSalaryEntryInput = {
  amount: Scalars['Float']['input'];
  employmentInfoId: Scalars['String']['input'];
  partTimePercentage: Scalars['Float']['input'];
  year: Scalars['Float']['input'];
};

export type CreateSystemSettingInput = {
  autoApproveChanges?: Scalars['Boolean']['input'];
  effectiveDate: Scalars['DateTime']['input'];
  passwordExpiryDays?: Scalars['Int']['input'];
  requireTwoFactorAuth?: Scalars['Boolean']['input'];
  sessionTimeout?: Scalars['Int']['input'];
  userId: Scalars['String']['input'];
};

export type CreateUserInput = {
  email: Scalars['String']['input'];
  firebaseUid?: InputMaybe<Scalars['String']['input']>;
  firstname?: InputMaybe<Scalars['String']['input']>;
  lastname?: InputMaybe<Scalars['String']['input']>;
  roleId: Scalars['String']['input'];
};

export type CreateVoluntaryContributionInput = {
  accumulatedInterest?: InputMaybe<Scalars['Float']['input']>;
  amount?: InputMaybe<Scalars['Float']['input']>;
  date?: InputMaybe<Scalars['DateTime']['input']>;
  pensionDataId: Scalars['String']['input'];
  type?: InputMaybe<Scalars['String']['input']>;
};

export type Document = {
  __typename?: 'Document';
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['output'];
};

export type EmploymentInfo = {
  __typename?: 'EmploymentInfo';
  department?: Maybe<Scalars['String']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  havNum?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  participant: Participant;
  position?: Maybe<Scalars['String']['output']>;
  regNum?: Maybe<Scalars['Float']['output']>;
  salaryEntries: Array<SalaryEntry>;
  startDate?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type ErrorType = {
  __typename?: 'ErrorType';
  code?: Maybe<Scalars['String']['output']>;
  message: Scalars['String']['output'];
};

export type FindAllAuditLogsInput = {
  action?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  entityId?: InputMaybe<Scalars['String']['input']>;
  entityType?: InputMaybe<Scalars['String']['input']>;
  proposalId?: InputMaybe<Scalars['String']['input']>;
  skip?: InputMaybe<Scalars['Float']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  take?: InputMaybe<Scalars['Float']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type FindAllCertifiedDataInput = {
  certificationYear?: InputMaybe<Scalars['Int']['input']>;
  participantId?: InputMaybe<Scalars['String']['input']>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
};

export type FindAllDocumentsInput = {
  participantId?: InputMaybe<Scalars['String']['input']>;
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  skip?: InputMaybe<Scalars['Float']['input']>;
  take?: InputMaybe<Scalars['Float']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type FindAllParticipantsInput = {
  searchTerm?: InputMaybe<Scalars['String']['input']>;
  skip?: InputMaybe<Scalars['Float']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  take?: InputMaybe<Scalars['Float']['input']>;
};

export type FindOneCertifiedDataInput = {
  certificationYear?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  participantId?: InputMaybe<Scalars['String']['input']>;
};

export type FirebaseUserDto = {
  __typename?: 'FirebaseUserDto';
  aud?: Maybe<Scalars['String']['output']>;
  auth_time?: Maybe<Scalars['Float']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  email_verified?: Maybe<Scalars['Boolean']['output']>;
  exp?: Maybe<Scalars['Float']['output']>;
  iat?: Maybe<Scalars['Float']['output']>;
  iss?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  pensionUserId?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Scalars['String']['output']>;
  roleId?: Maybe<Scalars['String']['output']>;
  sub?: Maybe<Scalars['String']['output']>;
  uid?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  approveChangeProposal: ChangeProposal;
  createAddress: Address;
  createAnnualAccrual: AnnualAccrual;
  createAuditLog: AuditLog;
  createCertificationRejectReason: CertificationRejectReason;
  createCertifiedData: CertifiedData;
  createCertifiedEmploymentInfo: CertifiedEmploymentInfo;
  createCertifiedIndexationStartOfYear: CertifiedIndexationStartOfYear;
  createCertifiedPensionCorrections: CertifiedPensionCorrections;
  createCertifiedPensionInfo: CertifiedPensionInfo;
  createCertifiedPensionParameters: CertifiedPensionParameters;
  createCertifiedPersonalInfo: CertifiedPersonalInfo;
  createCertifiedVoluntaryContributions: CertifiedVoluntaryContributions;
  createChangeData: ChangeData;
  createChangeProposal: ChangeProposal;
  createChild: Child;
  createConversionDetails: ConversionDetail;
  createDocument: Document;
  createEmploymentInfo: EmploymentInfo;
  createNotification: Notification;
  createParticipant: Participant;
  createPartnerInfo: PartnerInfo;
  createPensionCorrections: PensionCorrections;
  createPensionData: PensionData;
  createPensionInfo: PensionInfo;
  createPensionParameters: PensionParameters;
  createPersonalInfo: PersonalInfo;
  createRole: Role;
  createSalaryEntry: SalaryEntry;
  createSystemSetting: SystemSetting;
  createUser: User;
  createVoluntaryContribution: VoluntaryContribution;
  deleteAddress: Address;
  deleteAnnualAccrual: AnnualAccrual;
  deleteChangeData: ChangeData;
  deleteChangeProposal: ChangeProposal;
  deleteChild: Child;
  deleteConversionDetails: ConversionDetail;
  deleteEmploymentInfo: EmploymentInfo;
  deletePensionData: PensionData;
  deleteSalaryEntry: SalaryEntry;
  deleteVoluntaryContribution: VoluntaryContribution;
  getLatestApprovedChange?: Maybe<ChangeProposal>;
  markAllNotificationsAsRead: Scalars['Boolean']['output'];
  markNotificationAsRead: Notification;
  rejectChangeProposal: ChangeProposal;
  removeCertificationRejectReason: CertificationRejectReason;
  removeDocument: Document;
  removeNotification: Notification;
  removeParticipant: Participant;
  removePartnerInfo: PartnerInfo;
  removePensionCorrections: PensionCorrections;
  removePensionInfo: PensionInfo;
  removePensionParameters: PensionParameters;
  removePersonalInfo: PersonalInfo;
  removeRole: Role;
  removeSystemSetting: SystemSetting;
  removeUser: User;
  revertApprovedRejectedChanges: RevertChangesResponse;
  revertSingleField: RevertSingleFieldResponse;
  setUserClaims: ClaimsResponse;
  updateAddress: Address;
  updateAnnualAccrual: AnnualAccrual;
  updateCertificationStatus: CertifiedData;
  updateCertifiedChild: CertifiedChild;
  updateCertifiedDataApprovedChanges: CertifiedData;
  updateCertifiedDataRejectedChanges: CertifiedData;
  updateChangeData: ChangeData;
  updateChangeProposal: ChangeProposal;
  updateChild: Child;
  updateConversionDetails: ConversionDetail;
  updateDocument: Document;
  updateEmploymentInfo: EmploymentInfo;
  updateNotification: Notification;
  updateParticipant: Participant;
  updatePartnerInfo: PartnerInfo;
  updatePensionCorrections: PensionCorrections;
  updatePensionData: PensionData;
  updatePensionInfo: PensionInfo;
  updatePensionParameters: PensionParameters;
  updatePersonalInfo: PersonalInfo;
  updateRole: Role;
  updateSalaryEntry: SalaryEntry;
  updateSystemSetting: SystemSetting;
  updateUser: User;
  updateUserLastLogin: User;
  updateVoluntaryContribution: VoluntaryContribution;
};


export type MutationApproveChangeProposalArgs = {
  changePropagated?: InputMaybe<Scalars['Boolean']['input']>;
  changeProposalId: Scalars['String']['input'];
  reviewerId: Scalars['String']['input'];
};


export type MutationCreateAddressArgs = {
  createAddressInput: CreateAddressInput;
};


export type MutationCreateAnnualAccrualArgs = {
  createAnnualAccrualInput: CreateAnnualAccrualInput;
};


export type MutationCreateAuditLogArgs = {
  createAuditLogInput: CreateAuditLogInput;
};


export type MutationCreateCertificationRejectReasonArgs = {
  createCertificationRejectReasonInput: CreateCertificationRejectReasonInput;
};


export type MutationCreateCertifiedDataArgs = {
  certifiedById: Scalars['String']['input'];
  createCertifiedDataInput: CreateCertifiedDataInput;
  participantId: Scalars['String']['input'];
};


export type MutationCreateCertifiedEmploymentInfoArgs = {
  certifiedDataId: Scalars['String']['input'];
  createCertifiedEmploymentInfoInput: CreateCertifiedEmploymentInfoInput;
};


export type MutationCreateCertifiedIndexationStartOfYearArgs = {
  certifiedDataId: Scalars['String']['input'];
  createCertifiedIndexationInput: CreateCertifiedIndexationStartOfYearInput;
};


export type MutationCreateCertifiedPensionCorrectionsArgs = {
  certifiedDataId: Scalars['String']['input'];
  createCertifiedCorrectionsInput: CreateCertifiedPensionCorrectionsInput;
};


export type MutationCreateCertifiedPensionInfoArgs = {
  certifiedDataId: Scalars['String']['input'];
  createCertifiedPensionInfoInput: CreateCertifiedPensionInfoInput;
};


export type MutationCreateCertifiedPensionParametersArgs = {
  certifiedDataId: Scalars['String']['input'];
  createCertifiedParametersInput: CreateCertifiedPensionParametersInput;
};


export type MutationCreateCertifiedPersonalInfoArgs = {
  certifiedDataId: Scalars['String']['input'];
  createCertifiedPersonalInfoInput: CreateCertifiedPersonalInfoInput;
};


export type MutationCreateCertifiedVoluntaryContributionsArgs = {
  certifiedDataId: Scalars['String']['input'];
  createCertifiedContributionsInput: CreateCertifiedVoluntaryContributionsInput;
};


export type MutationCreateChangeDataArgs = {
  createChangeDataInput: CreateChangeDataInput;
};


export type MutationCreateChangeProposalArgs = {
  createChangeDataInput?: InputMaybe<CreateChangeDataInput>;
  createChangeProposalInput: CreateChangeProposalInput;
};


export type MutationCreateChildArgs = {
  createChildInput: CreateChildInput;
};


export type MutationCreateConversionDetailsArgs = {
  createConversionDetailsInput: CreateConversionDetailsInput;
};


export type MutationCreateDocumentArgs = {
  createDocumentInput: CreateDocumentInput;
};


export type MutationCreateEmploymentInfoArgs = {
  createEmploymentInfoInput: CreateEmploymentInfoInput;
};


export type MutationCreateNotificationArgs = {
  createNotificationInput: CreateNotificationInput;
};


export type MutationCreateParticipantArgs = {
  createParticipantInput: CreateParticipantInput;
};


export type MutationCreatePartnerInfoArgs = {
  createPartnerInfoInput: CreatePartnerInfoInput;
};


export type MutationCreatePensionCorrectionsArgs = {
  createPensionCorrectionsInput: CreatePensionCorrectionsInput;
};


export type MutationCreatePensionDataArgs = {
  createPensionDataInput: CreatePensionDataInput;
};


export type MutationCreatePensionInfoArgs = {
  createPensionInfoInput: CreatePensionInfoInput;
};


export type MutationCreatePensionParametersArgs = {
  createPensionParametersInput: CreatePensionParametersInput;
};


export type MutationCreatePersonalInfoArgs = {
  createPersonalInfoInput: CreatePersonalInfoInput;
};


export type MutationCreateRoleArgs = {
  createRoleInput: CreateRoleInput;
};


export type MutationCreateSalaryEntryArgs = {
  createSalaryEntryInput: CreateSalaryEntryInput;
};


export type MutationCreateSystemSettingArgs = {
  createSystemSettingInput: CreateSystemSettingInput;
};


export type MutationCreateUserArgs = {
  createUserInput: CreateUserInput;
};


export type MutationCreateVoluntaryContributionArgs = {
  createVoluntaryContributionInput: CreateVoluntaryContributionInput;
};


export type MutationDeleteAddressArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteAnnualAccrualArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteChangeDataArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteChangeProposalArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteChildArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteConversionDetailsArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteEmploymentInfoArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeletePensionDataArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteSalaryEntryArgs = {
  id: Scalars['String']['input'];
};


export type MutationDeleteVoluntaryContributionArgs = {
  id: Scalars['String']['input'];
};


export type MutationGetLatestApprovedChangeArgs = {
  entityType: Scalars['String']['input'];
  path: Scalars['String']['input'];
};


export type MutationMarkAllNotificationsAsReadArgs = {
  recipientId: Scalars['String']['input'];
};


export type MutationMarkNotificationAsReadArgs = {
  id: Scalars['String']['input'];
};


export type MutationRejectChangeProposalArgs = {
  changeProposalId: Scalars['String']['input'];
  reviewerComments: Scalars['String']['input'];
  reviewerId: Scalars['String']['input'];
};


export type MutationRemoveCertificationRejectReasonArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveDocumentArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveNotificationArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveParticipantArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemovePartnerInfoArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemovePensionCorrectionsArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemovePensionInfoArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemovePensionParametersArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemovePersonalInfoArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemoveRoleArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemoveSystemSettingArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveUserArgs = {
  id: Scalars['String']['input'];
};


export type MutationRevertApprovedRejectedChangesArgs = {
  certificationYear: Scalars['Int']['input'];
};


export type MutationRevertSingleFieldArgs = {
  input: RevertSingleFieldInput;
};


export type MutationSetUserClaimsArgs = {
  firebaseUid: Scalars['String']['input'];
};


export type MutationUpdateAddressArgs = {
  updateAddressInput: UpdateAddressInput;
};


export type MutationUpdateAnnualAccrualArgs = {
  updateAnnualAccrualInput: UpdateAnnualAccrualInput;
};


export type MutationUpdateCertificationStatusArgs = {
  input: UpdateCertificationStatusInput;
};


export type MutationUpdateCertifiedChildArgs = {
  updateCertifiedChildInput: UpdateCertifiedChildInput;
};


export type MutationUpdateCertifiedDataApprovedChangesArgs = {
  approvedChanges: Array<Scalars['String']['input']>;
  entityId?: InputMaybe<Scalars['String']['input']>;
  entityType?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
};


export type MutationUpdateCertifiedDataRejectedChangesArgs = {
  entityId?: InputMaybe<Scalars['String']['input']>;
  entityType?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  rejectReason?: InputMaybe<Scalars['String']['input']>;
  rejectedChanges: Array<Scalars['String']['input']>;
};


export type MutationUpdateChangeDataArgs = {
  updateChangeDataInput: UpdateChangeDataInput;
};


export type MutationUpdateChangeProposalArgs = {
  updateChangeProposalInput: UpdateChangeProposalInput;
};


export type MutationUpdateChildArgs = {
  updateChildInput: UpdateChildInput;
};


export type MutationUpdateConversionDetailsArgs = {
  updateConversionDetailsInput: UpdateConversionDetailsInput;
};


export type MutationUpdateDocumentArgs = {
  updateDocumentInput: UpdateDocumentInput;
};


export type MutationUpdateEmploymentInfoArgs = {
  updateEmploymentInfoInput: UpdateEmploymentInfoInput;
};


export type MutationUpdateNotificationArgs = {
  updateNotificationInput: UpdateNotificationInput;
};


export type MutationUpdateParticipantArgs = {
  updateParticipantInput: UpdateParticipantInput;
};


export type MutationUpdatePartnerInfoArgs = {
  updatePartnerInfoInput: UpdatePartnerInfoInput;
};


export type MutationUpdatePensionCorrectionsArgs = {
  updatePensionCorrectionsInput: UpdatePensionCorrectionsInput;
};


export type MutationUpdatePensionDataArgs = {
  updatePensionDataInput: UpdatePensionDataInput;
};


export type MutationUpdatePensionInfoArgs = {
  updatePensionInfoInput: UpdatePensionInfoInput;
};


export type MutationUpdatePensionParametersArgs = {
  updatePensionParametersInput: UpdatePensionParametersInput;
};


export type MutationUpdatePersonalInfoArgs = {
  updatePersonalInfoInput: UpdatePersonalInfoInput;
};


export type MutationUpdateRoleArgs = {
  updateRoleInput: UpdateRoleInput;
};


export type MutationUpdateSalaryEntryArgs = {
  updateSalaryEntryInput: UpdateSalaryEntryInput;
};


export type MutationUpdateSystemSettingArgs = {
  updateSystemSettingInput: UpdateSystemSettingInput;
};


export type MutationUpdateUserArgs = {
  updateUserInput: UpdateUserInput;
};


export type MutationUpdateUserLastLoginArgs = {
  id: Scalars['String']['input'];
};


export type MutationUpdateVoluntaryContributionArgs = {
  updateVoluntaryContributionInput: UpdateVoluntaryContributionInput;
};

export type NewUserInput = {
  email: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  resetLink: Scalars['String']['input'];
};

export type Notification = {
  __typename?: 'Notification';
  createdAt: Scalars['DateTime']['output'];
  createdBy: User;
  createdById: Scalars['String']['output'];
  entityId: Scalars['String']['output'];
  entityType: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  message: Scalars['String']['output'];
  read: Scalars['Boolean']['output'];
  readAt?: Maybe<Scalars['DateTime']['output']>;
  recipient: User;
  recipientId: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type PaginatedAuditLogs = {
  __typename?: 'PaginatedAuditLogs';
  items: Array<AuditLog>;
  totalCount: Scalars['Int']['output'];
};

export type PaginatedCertifiedData = {
  __typename?: 'PaginatedCertifiedData';
  items: Array<CertifiedData>;
  totalCount: Scalars['Int']['output'];
};

export type PaginatedParticipants = {
  __typename?: 'PaginatedParticipants';
  items: Array<Participant>;
  totalCount: Scalars['Int']['output'];
};

export type Participant = {
  __typename?: 'Participant';
  certifiedData?: Maybe<Array<CertifiedData>>;
  changeProposals?: Maybe<Array<ChangeProposal>>;
  createdAt: Scalars['DateTime']['output'];
  createdBy: Scalars['String']['output'];
  documents?: Maybe<Array<Document>>;
  employmentInfo?: Maybe<EmploymentInfo>;
  id: Scalars['ID']['output'];
  lastModified?: Maybe<Scalars['DateTime']['output']>;
  pensionData?: Maybe<PensionData>;
  pensionInfo?: Maybe<PensionInfo>;
  personalInfo?: Maybe<PersonalInfo>;
  status: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
  updatedBy: Scalars['String']['output'];
};

export type ParticipantCreateNestedOneWithoutCertifiedDataInput = {
  connect: Scalars['String']['input'];
};

export type PartnerInfo = {
  __typename?: 'PartnerInfo';
  dateOfBirth?: Maybe<Scalars['DateTime']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  isCurrent: Scalars['Boolean']['output'];
  isDeceased: Scalars['Boolean']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  personalInfoId: Scalars['String']['output'];
  startDate?: Maybe<Scalars['DateTime']['output']>;
};

export type PensionCorrections = {
  __typename?: 'PensionCorrections';
  correction: Scalars['Float']['output'];
  createdAt: Scalars['DateTime']['output'];
  createdBy: User;
  id: Scalars['ID']['output'];
  reviewedAt: Scalars['DateTime']['output'];
  reviewedBy: User;
  updatedAt: Scalars['DateTime']['output'];
  year: Scalars['String']['output'];
};

export type PensionData = {
  __typename?: 'PensionData';
  annualAccrual?: Maybe<AnnualAccrual>;
  conversionDetails?: Maybe<ConversionDetail>;
  id: Scalars['ID']['output'];
  participant: Participant;
  pensionParameters?: Maybe<PensionParameters>;
  pensionableAmount?: Maybe<Scalars['Float']['output']>;
  retirementDate?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  totalContributions?: Maybe<Scalars['Float']['output']>;
  voluntaryContributions: Array<VoluntaryContribution>;
};

export type PensionInfo = {
  __typename?: 'PensionInfo';
  accruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  accruedGrossAnnualSinglesPension?: Maybe<Scalars['Float']['output']>;
  attainableGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  code?: Maybe<Scalars['Int']['output']>;
  codeDescription?: Maybe<Scalars['String']['output']>;
  codeEffectiveDate?: Maybe<Scalars['DateTime']['output']>;
  codeImpact?: Maybe<Scalars['String']['output']>;
  extraAccruedGrossAnnualOldAgePension?: Maybe<Scalars['Float']['output']>;
  extraAccruedGrossAnnualPartnersPension?: Maybe<Scalars['Float']['output']>;
  grossAnnualDisabilityPension?: Maybe<Scalars['Float']['output']>;
  grossAnnualPension?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  participant: Participant;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  pensionBase?: Maybe<Scalars['Float']['output']>;
  previousCode?: Maybe<Scalars['Int']['output']>;
  previousCodeEffectiveDate?: Maybe<Scalars['DateTime']['output']>;
};

export type PensionParameters = {
  __typename?: 'PensionParameters';
  accrualPercentage: Scalars['Float']['output'];
  annualMultiplier: Scalars['Float']['output'];
  createdAt: Scalars['DateTime']['output'];
  effectiveDate: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  offsetAmount: Scalars['Float']['output'];
  partnersPensionPercentage: Scalars['Float']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  pensionData?: Maybe<PensionData>;
  pensionDataId: Scalars['String']['output'];
  retirementAge: Scalars['Int']['output'];
  updatedAt: Scalars['DateTime']['output'];
  updatedBy: User;
  userId: Scalars['String']['output'];
  voluntaryContributionInterestRate: Scalars['Float']['output'];
  year: Scalars['String']['output'];
};

export type PersonalInfo = {
  __typename?: 'PersonalInfo';
  address?: Maybe<Address>;
  birthDay?: Maybe<Scalars['Int']['output']>;
  birthMonth?: Maybe<Scalars['Int']['output']>;
  birthYear?: Maybe<Scalars['Int']['output']>;
  children?: Maybe<Array<Child>>;
  email?: Maybe<Scalars['String']['output']>;
  firstName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  lastName: Scalars['String']['output'];
  maritalStatus?: Maybe<Scalars['String']['output']>;
  participantId: Scalars['String']['output'];
  partnerInfo?: Maybe<Array<PartnerInfo>>;
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  phone?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  address: Address;
  addresses: Array<Address>;
  annualAccrual: AnnualAccrual;
  annualAccruals: Array<AnnualAccrual>;
  auditLog: AuditLog;
  auditLogs: PaginatedAuditLogs;
  certificationRejectReason: CertificationRejectReason;
  certificationRejectReasons: Array<CertificationRejectReason>;
  certifiedAddress: CertifiedAddress;
  certifiedContributionsByCertifiedDataId: CertifiedVoluntaryContributions;
  certifiedCorrectionsByCertifiedDataId: CertifiedPensionCorrections;
  certifiedEmploymentInfo: CertifiedEmploymentInfo;
  certifiedEmploymentInfoByCertifiedDataId: CertifiedEmploymentInfo;
  certifiedIndexationByCertifiedDataId: CertifiedIndexationStartOfYear;
  certifiedIndexationStartOfYear: CertifiedIndexationStartOfYear;
  certifiedParametersByCertifiedDataId: CertifiedPensionParameters;
  certifiedPartnerInfo: CertifiedPartnerInfo;
  certifiedPensionCorrections: CertifiedPensionCorrections;
  certifiedPensionInfo: CertifiedPensionInfo;
  certifiedPensionInfoByCertifiedDataId: CertifiedPensionInfo;
  certifiedPensionParameters: CertifiedPensionParameters;
  certifiedPersonalInfo: CertifiedPersonalInfo;
  certifiedPersonalInfoByCertifiedDataId: CertifiedPersonalInfo;
  certifiedVoluntaryContributions: CertifiedVoluntaryContributions;
  child: Child;
  conversionDetails: ConversionDetail;
  document: Document;
  documents: Array<Document>;
  employmentInfo: EmploymentInfo;
  employmentInfos: Array<EmploymentInfo>;
  entityAuditLogs: Array<AuditLog>;
  findOneChangeProposal: ChangeProposal;
  getAllCertifiedData: PaginatedCertifiedData;
  getAllChangeProposals: Array<ChangeProposal>;
  getAllNotifications: Array<Notification>;
  getAllParticipantChangeProposals: Array<ChangeProposal>;
  getAllParticipantChangeProposalsHistory: Array<ChangeProposal>;
  getAllParticipants: Array<Participant>;
  getAllParticipantsWithFilter: PaginatedParticipants;
  getAllPensionParamChangeProposals: Array<ChangeProposal>;
  getAllPensionParamChangeProposalsHistory: Array<ChangeProposal>;
  getAllPensionParameters: Array<PensionParameters>;
  getAllSystemSettings: Array<SystemSetting>;
  getAllUsers: Array<User>;
  getCertifiedDataById: CertifiedData;
  getCertifiedDataByYearAndYearBefore: CertifiedDataByYearResponse;
  getLatestSystemSetting: SystemSetting;
  getNotificationsByRecipient: Array<Notification>;
  getParticipantById: Participant;
  getParticipantCertifiedData: Array<CertifiedData>;
  getPensionChildren: Array<Child>;
  getPensionParamById: PensionParameters;
  getSystemSettingById: SystemSetting;
  getUserByEmail: User;
  getUserById: User;
  latestParticipantCertification?: Maybe<CertifiedData>;
  participantDocuments: Array<Document>;
  partnerInfo: PartnerInfo;
  partnerInfos: Array<PartnerInfo>;
  pensionCorrection: PensionCorrections;
  pensionCorrections: Array<PensionCorrections>;
  pensionData: PensionData;
  pensionInfo: PensionInfo;
  pensionInfos: Array<PensionInfo>;
  personalInfo: PersonalInfo;
  personalInfos: Array<PersonalInfo>;
  previewRevertChanges: RevertChangesPreview;
  role: Role;
  roles: Array<Role>;
  salaryEntries: Array<SalaryEntry>;
  salaryEntry: SalaryEntry;
  sendNewUserEmail: Scalars['String']['output'];
  sendRestPasswordEmail: Scalars['String']['output'];
  timeRangeAuditLogs: Array<AuditLog>;
  totalAuditLogs: Scalars['Int']['output'];
  totalParticipants: Scalars['Int']['output'];
  unreadNotificationCount: Scalars['Int']['output'];
  unreadNotifications: Array<Notification>;
  userAuditLogs: Array<AuditLog>;
  userByFirebaseUid: User;
  voluntaryContribution: VoluntaryContribution;
  voluntaryContributions: Array<VoluntaryContribution>;
  yearCertifications: Array<CertifiedData>;
};


export type QueryAddressArgs = {
  id: Scalars['String']['input'];
};


export type QueryAnnualAccrualArgs = {
  id: Scalars['String']['input'];
};


export type QueryAuditLogArgs = {
  id: Scalars['String']['input'];
};


export type QueryAuditLogsArgs = {
  findAllAuditLogsInput?: InputMaybe<FindAllAuditLogsInput>;
};


export type QueryCertificationRejectReasonArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedAddressArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedContributionsByCertifiedDataIdArgs = {
  certifiedDataId: Scalars['String']['input'];
};


export type QueryCertifiedCorrectionsByCertifiedDataIdArgs = {
  certifiedDataId: Scalars['String']['input'];
};


export type QueryCertifiedEmploymentInfoArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedEmploymentInfoByCertifiedDataIdArgs = {
  certifiedDataId: Scalars['String']['input'];
};


export type QueryCertifiedIndexationByCertifiedDataIdArgs = {
  certifiedDataId: Scalars['String']['input'];
};


export type QueryCertifiedIndexationStartOfYearArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedParametersByCertifiedDataIdArgs = {
  certifiedDataId: Scalars['String']['input'];
};


export type QueryCertifiedPartnerInfoArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedPensionCorrectionsArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedPensionInfoArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedPensionInfoByCertifiedDataIdArgs = {
  certifiedDataId: Scalars['String']['input'];
};


export type QueryCertifiedPensionParametersArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedPersonalInfoArgs = {
  id: Scalars['String']['input'];
};


export type QueryCertifiedPersonalInfoByCertifiedDataIdArgs = {
  certifiedDataId: Scalars['String']['input'];
};


export type QueryCertifiedVoluntaryContributionsArgs = {
  id: Scalars['String']['input'];
};


export type QueryChildArgs = {
  id: Scalars['String']['input'];
};


export type QueryConversionDetailsArgs = {
  id: Scalars['String']['input'];
};


export type QueryDocumentArgs = {
  id: Scalars['String']['input'];
};


export type QueryDocumentsArgs = {
  findAllDocumentsInput?: InputMaybe<FindAllDocumentsInput>;
};


export type QueryEmploymentInfoArgs = {
  id: Scalars['String']['input'];
};


export type QueryEntityAuditLogsArgs = {
  entityId: Scalars['String']['input'];
  entityType: Scalars['String']['input'];
};


export type QueryFindOneChangeProposalArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetAllCertifiedDataArgs = {
  findAllCertifiedDataInput?: InputMaybe<FindAllCertifiedDataInput>;
};


export type QueryGetAllParticipantChangeProposalsArgs = {
  reviewerId: Scalars['String']['input'];
};


export type QueryGetAllParticipantChangeProposalsHistoryArgs = {
  reviewerId: Scalars['String']['input'];
};


export type QueryGetAllParticipantsWithFilterArgs = {
  findAllParticipantsInput?: InputMaybe<FindAllParticipantsInput>;
};


export type QueryGetAllPensionParamChangeProposalsArgs = {
  reviewerId: Scalars['String']['input'];
};


export type QueryGetAllPensionParamChangeProposalsHistoryArgs = {
  reviewerId: Scalars['String']['input'];
};


export type QueryGetCertifiedDataByIdArgs = {
  findOneCertifiedDataInput: FindOneCertifiedDataInput;
};


export type QueryGetCertifiedDataByYearAndYearBeforeArgs = {
  certificationYear: Scalars['Int']['input'];
};


export type QueryGetNotificationsByRecipientArgs = {
  recipientId: Scalars['String']['input'];
};


export type QueryGetParticipantByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetParticipantCertifiedDataArgs = {
  participantId: Scalars['String']['input'];
};


export type QueryGetPensionParamByIdArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetSystemSettingByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetUserByEmailArgs = {
  email: Scalars['String']['input'];
};


export type QueryGetUserByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryLatestParticipantCertificationArgs = {
  participantId: Scalars['String']['input'];
};


export type QueryParticipantDocumentsArgs = {
  participantId: Scalars['String']['input'];
};


export type QueryPartnerInfoArgs = {
  id: Scalars['ID']['input'];
};


export type QueryPensionCorrectionArgs = {
  id: Scalars['ID']['input'];
};


export type QueryPensionDataArgs = {
  id: Scalars['String']['input'];
};


export type QueryPensionInfoArgs = {
  id: Scalars['ID']['input'];
};


export type QueryPersonalInfoArgs = {
  id: Scalars['ID']['input'];
};


export type QueryPreviewRevertChangesArgs = {
  certificationYear: Scalars['Int']['input'];
};


export type QueryRoleArgs = {
  id: Scalars['ID']['input'];
};


export type QuerySalaryEntryArgs = {
  id: Scalars['String']['input'];
};


export type QuerySendNewUserEmailArgs = {
  data: NewUserInput;
};


export type QuerySendRestPasswordEmailArgs = {
  data: ResetPasswordInput;
};


export type QueryTimeRangeAuditLogsArgs = {
  endDate: Scalars['DateTime']['input'];
  startDate: Scalars['DateTime']['input'];
};


export type QueryTotalAuditLogsArgs = {
  entityType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTotalParticipantsArgs = {
  status?: InputMaybe<Scalars['String']['input']>;
};


export type QueryUserAuditLogsArgs = {
  userId: Scalars['String']['input'];
};


export type QueryUserByFirebaseUidArgs = {
  firebaseUid: Scalars['String']['input'];
};


export type QueryVoluntaryContributionArgs = {
  id: Scalars['String']['input'];
};


export type QueryYearCertificationsArgs = {
  certificationYear: Scalars['Int']['input'];
};

export type ResetPasswordInput = {
  email: Scalars['String']['input'];
  resetLink: Scalars['String']['input'];
};

export type RevertChangesPreview = {
  __typename?: 'RevertChangesPreview';
  affectedEntities: Array<RevertChangesPreviewItem>;
  certificationYear: Scalars['Int']['output'];
  estimatedImpact: Scalars['String']['output'];
  totalCertifiedDataRecords: Scalars['Int']['output'];
  totalEntitiesAffected: Scalars['Int']['output'];
  totalRejectionReasons: Scalars['Int']['output'];
};

export type RevertChangesPreviewItem = {
  __typename?: 'RevertChangesPreviewItem';
  approvedChanges: Array<Scalars['String']['output']>;
  approvedChangesCount: Scalars['Int']['output'];
  certifiedDataId: Scalars['String']['output'];
  entityId: Scalars['String']['output'];
  entityType: Scalars['String']['output'];
  participantName: Scalars['String']['output'];
  requestedChanges: Array<Scalars['String']['output']>;
  requestedChangesCount: Scalars['Int']['output'];
};

export type RevertChangesResponse = {
  __typename?: 'RevertChangesResponse';
  certificationYear: Scalars['Int']['output'];
  message: Scalars['String']['output'];
  revertedEntities?: Maybe<Array<RevertedEntityInfo>>;
  success: Scalars['Boolean']['output'];
  totalCertifiedDataRecords: Scalars['Int']['output'];
  totalEntitiesReverted: Scalars['Int']['output'];
  totalRejectionReasonsDeleted: Scalars['Int']['output'];
};

export type RevertSingleFieldInput = {
  entityId: Scalars['String']['input'];
  entityType: Scalars['String']['input'];
  path: Scalars['String']['input'];
};

export type RevertSingleFieldResponse = {
  __typename?: 'RevertSingleFieldResponse';
  entityId: Scalars['String']['output'];
  entityType: Scalars['String']['output'];
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
  success: Scalars['Boolean']['output'];
};

export type RevertedEntityInfo = {
  __typename?: 'RevertedEntityInfo';
  approvedChangesCleared: Array<Scalars['String']['output']>;
  certifiedDataId: Scalars['String']['output'];
  entityId: Scalars['String']['output'];
  entityType: Scalars['String']['output'];
  requestedChangesCleared: Array<Scalars['String']['output']>;
};

export type Role = {
  __typename?: 'Role';
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type SalaryEntry = {
  __typename?: 'SalaryEntry';
  amount: Scalars['Float']['output'];
  employmentInfo: EmploymentInfo;
  id: Scalars['ID']['output'];
  partTimePercentage: Scalars['Float']['output'];
  pendingChanges?: Maybe<Array<Scalars['String']['output']>>;
  year: Scalars['Float']['output'];
};

export type Subscription = {
  __typename?: 'Subscription';
  userNotifications: Notification;
};

export type SystemSetting = {
  __typename?: 'SystemSetting';
  autoApproveChanges: Scalars['Boolean']['output'];
  createdAt: Scalars['DateTime']['output'];
  effectiveDate: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  passwordExpiryDays: Scalars['Int']['output'];
  requireTwoFactorAuth: Scalars['Boolean']['output'];
  sessionTimeout: Scalars['Int']['output'];
  updatedAt: Scalars['DateTime']['output'];
  updatedBy: User;
  userId: Scalars['String']['output'];
};

export type UpdateAddressInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  houseNumber?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  personalInfoId?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  street?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAnnualAccrualInput = {
  employeeContributions?: InputMaybe<Scalars['Float']['input']>;
  employerContributions?: InputMaybe<Scalars['Float']['input']>;
  franchise?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['String']['input'];
  monthlyBenefit?: InputMaybe<Scalars['Float']['input']>;
  pensionDataId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCertificationStatusInput = {
  id: Scalars['String']['input'];
  status: Scalars['String']['input'];
};

export type UpdateCertifiedChildInput = {
  certificationRejectReason?: InputMaybe<Array<CreateCertificationRejectReasonInput>>;
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  isOrphan?: InputMaybe<Scalars['Boolean']['input']>;
  isStudying?: InputMaybe<Scalars['Boolean']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateChangeDataInput = {
  changeProposalId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  newValue?: InputMaybe<Scalars['String']['input']>;
  oldValue?: InputMaybe<Scalars['String']['input']>;
  path?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateChangeProposalInput = {
  changePropagated?: InputMaybe<Scalars['Boolean']['input']>;
  createdById?: InputMaybe<Scalars['String']['input']>;
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
  employmentInfoId?: InputMaybe<Scalars['String']['input']>;
  entityId?: InputMaybe<Scalars['String']['input']>;
  entityType?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  isCertificationProposal?: InputMaybe<Scalars['Boolean']['input']>;
  participantName?: InputMaybe<Scalars['String']['input']>;
  reviewComments?: InputMaybe<Scalars['String']['input']>;
  reviewedById?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<ChangeStatus>;
  type?: InputMaybe<ChangeType>;
};

export type UpdateChildInput = {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  isOrphan?: InputMaybe<Scalars['Boolean']['input']>;
  isStudying?: InputMaybe<Scalars['Boolean']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  personalInfoId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateConversionDetailsInput = {
  conversionDate?: InputMaybe<Scalars['DateTime']['input']>;
  conversionRate?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['String']['input'];
  oldAgePensionIncrease?: InputMaybe<Scalars['Float']['input']>;
  partnerPensionIncrease?: InputMaybe<Scalars['Float']['input']>;
  pensionDataId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateDocumentInput = {
  createdBy?: InputMaybe<Scalars['String']['input']>;
  documentId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  mimeType?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  participantId?: InputMaybe<Scalars['String']['input']>;
  path?: InputMaybe<Scalars['String']['input']>;
  size?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  uploadedAt?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdateEmploymentInfoInput = {
  department?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  havNum?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['String']['input'];
  participantId?: InputMaybe<Scalars['String']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
  regNum?: InputMaybe<Scalars['Float']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateNotificationInput = {
  entityId?: InputMaybe<Scalars['String']['input']>;
  entityType?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  message?: InputMaybe<Scalars['String']['input']>;
  read?: InputMaybe<Scalars['Boolean']['input']>;
  recipientId?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateParticipantInput = {
  createdBy?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  lastModified?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  updatedBy?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePartnerInfoInput = {
  dateOfBirth?: InputMaybe<Scalars['DateTime']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  isCurrent: Scalars['Boolean']['input'];
  isDeceased: Scalars['Boolean']['input'];
  lastName?: InputMaybe<Scalars['String']['input']>;
  personalInfoId: Scalars['String']['input'];
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdatePensionCorrectionsInput = {
  correction?: InputMaybe<Scalars['Float']['input']>;
  createdById?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['String']['input'];
  reviewedAt?: InputMaybe<Scalars['DateTime']['input']>;
  reviewedById?: InputMaybe<Scalars['ID']['input']>;
  year?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePensionDataInput = {
  id: Scalars['String']['input'];
  participantId?: InputMaybe<Scalars['String']['input']>;
  pensionableAmount?: InputMaybe<Scalars['Float']['input']>;
  retirementDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  totalContributions?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdatePensionInfoInput = {
  code?: InputMaybe<Scalars['Int']['input']>;
  codeDescription?: InputMaybe<Scalars['String']['input']>;
  codeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
  codeImpact?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  participantId?: InputMaybe<Scalars['ID']['input']>;
  previousCode?: InputMaybe<Scalars['Int']['input']>;
  previousCodeEffectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdatePensionParametersInput = {
  accrualPercentage?: InputMaybe<Scalars['Float']['input']>;
  annualMultiplier?: InputMaybe<Scalars['Float']['input']>;
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['String']['input'];
  offsetAmount?: InputMaybe<Scalars['Float']['input']>;
  partnersPensionPercentage?: InputMaybe<Scalars['Float']['input']>;
  pensionDataId?: InputMaybe<Scalars['String']['input']>;
  retirementAge?: InputMaybe<Scalars['Int']['input']>;
  userId?: InputMaybe<Scalars['ID']['input']>;
  voluntaryContributionInterestRate?: InputMaybe<Scalars['Float']['input']>;
  year?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePersonalInfoInput = {
  birthDay?: InputMaybe<Scalars['Int']['input']>;
  birthMonth?: InputMaybe<Scalars['Int']['input']>;
  birthYear?: InputMaybe<Scalars['Int']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  lastName?: InputMaybe<Scalars['String']['input']>;
  maritalStatus?: InputMaybe<Scalars['String']['input']>;
  participantId?: InputMaybe<Scalars['String']['input']>;
  pendingChanges?: InputMaybe<Array<Scalars['String']['input']>>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateRoleInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateSalaryEntryInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  employmentInfoId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  partTimePercentage?: InputMaybe<Scalars['Float']['input']>;
  year?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateSystemSettingInput = {
  autoApproveChanges?: InputMaybe<Scalars['Boolean']['input']>;
  effectiveDate?: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['String']['input'];
  passwordExpiryDays?: InputMaybe<Scalars['Int']['input']>;
  requireTwoFactorAuth?: InputMaybe<Scalars['Boolean']['input']>;
  sessionTimeout?: InputMaybe<Scalars['Int']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  firebaseUid?: InputMaybe<Scalars['String']['input']>;
  firstname?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  lastname?: InputMaybe<Scalars['String']['input']>;
  roleId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateVoluntaryContributionInput = {
  accumulatedInterest?: InputMaybe<Scalars['Float']['input']>;
  amount?: InputMaybe<Scalars['Float']['input']>;
  date?: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['String']['input'];
  pensionDataId?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type User = {
  __typename?: 'User';
  email: Scalars['String']['output'];
  firebaseUid: Scalars['String']['output'];
  firstname?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  lastLogin?: Maybe<Scalars['DateTime']['output']>;
  lastname?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Role>;
};

export type UserCreateNestedOneWithoutCertifiedDataInput = {
  connect: Scalars['String']['input'];
};

export type VoluntaryContribution = {
  __typename?: 'VoluntaryContribution';
  accumulatedInterest?: Maybe<Scalars['Float']['output']>;
  amount?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  pensionData: PensionData;
  type?: Maybe<Scalars['String']['output']>;
};
