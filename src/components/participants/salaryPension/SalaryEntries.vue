
<script setup lang="ts">
  import { useQuery } from '@vue/apollo-composable'
  import { GET_PARTICIPANT_BY_ID } from '@/api/graphql/queries/participantQueries'
  import { ChangeType, SalaryEntry } from '@/gql/graphql'
  import { useAppStore } from '@/stores/app/appStore'
  import { usePensionStore } from '@/stores/pension/pensionStore'

  const props = defineProps({
    participantId: {
      type: String,
      required: true
    },
    employmentInfoId: {
      type: String,
      required: true
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  })

  const emit = defineEmits(['refresh'])

  const entries = ref<SalaryEntry[]>([])
  const loading = ref(true)
  const dialogVisible = ref(false)
  const participantName = ref('')
  const selectedEntry = ref<SalaryEntry | null>(null)
  const currentYear = new Date().getFullYear()
  const appStore = useAppStore()
  const pensionStore = usePensionStore()

  const certifiedYears = computed(() => {
    return pensionStore.certifiedDataYears
  })

  const { result, loading: queryLoading, refetch } = useQuery(
    GET_PARTICIPANT_BY_ID,
    { id: props.participantId },
    { fetchPolicy: 'network-only' }
  )

  // Format currency values
  const formatCurrency = (value: number): string => {
    return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  // Check if entry is from current year
  const isCurrentYear = (year: number): boolean => {
    return year === currentYear
  }

  // Helper function to determine status color
  const getStatusColor = (year: number): string => {
    if (isCurrentYear(year)) return 'primary'
    return 'success'
  }

  const isDisabled =  (pendingChanges: string[])=>{
      return pendingChanges.includes('amount')
  }

  const isCertifiedYear = (year: number) => {
    return certifiedYears.value.includes(year)
  }

  const fetchEntries = async () => {
    loading.value = true
    try {
      await refetch()
      if (result.value?.getParticipantById?.employmentInfo?.salaryEntries) {
        const sortedEntries = [...result.value.getParticipantById.employmentInfo.salaryEntries].sort((a: any, b: any) => b.year - a.year)
        entries.value = sortedEntries
        participantName.value = result.value.getParticipantById.personalInfo?.firstName + ' ' + result.value.getParticipantById.personalInfo?.lastName
      } else {
        entries.value = []
      }
      emit('refresh')
    } catch (error) {
      console.error('Error fetching salary entries:', error)
      entries.value = []
    } finally {
      loading.value = false
    }
  }

  const openEditDialog = (entry: SalaryEntry) => {
    if(isDisabled(entry.pendingChanges)) {
      appStore.showSnack('Sorry you cannot edit this field.')
      return
    }
    selectedEntry.value = entry
    dialogVisible.value = true
  }

  onMounted(fetchEntries)
</script>

<template>
  <v-card variant="outlined" class="mb-4">
    <v-card-title class="py-3">
      <h4 class="text-subtitle-1 font-weight-medium">Gross Part-time Monthly Salary</h4>
    </v-card-title>
    <v-card-subtitle class="text-caption text-medium-emphasis">
      Amount as of January 1st or start date if started during the year
    </v-card-subtitle>
    <v-card-text>
      <v-table class="salary-table" density="comfortable">
        <thead>
          <tr>
            <th class="text-left">YEAR</th>
            <th class="text-left">GROSS PART-TIME MONTHLY SALARY</th>
            <th class="text-center">STATUS</th>
            <th v-if="canEdit" class="text-center">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td colspan="4" class="text-center">
              <v-progress-circular indeterminate color="primary" />
            </td>
          </tr>
          <tr v-else-if="entries.length === 0">
            <td colspan="4" class="text-center">No salary entries found</td>
          </tr>
          <tr v-for="entry in entries" :key="entry.id" :class="isCurrentYear(entry.year) ? 'bg-blue-lighten-5' : ''">
            <td class="font-weight-medium">{{ entry.year }}</td>
            <td>Afl. {{ formatCurrency(entry.amount) }}</td>
            <td class="text-center">
              <v-chip v-if="isCurrentYear(entry.year)" :color="getStatusColor(entry.year)" size="small" label>
                Current
              </v-chip>
              <v-chip v-if="isCertifiedYear(entry.year)" :color="getStatusColor(entry.year)" size="small" label>
                Certified
              </v-chip>
            </td>
            <td v-if="canEdit" class="text-center">
              <v-btn
                v-if="!isCertifiedYear(entry.year)"
                icon
                size="small"
                variant="text"
                color="primary"
                @click="openEditDialog(entry)"
              >
                <VIcon
                  v-if="isDisabled(entry.pendingChanges)"
                  size="16"
                  icon="tabler-alert-triangle"
                  class="edit-icon"
                  color="error"
                />
                <VIcon
                  v-else
                  size="16"
                  icon="tabler-edit"
                  class="edit-icon"
                  color="primary"
                />
              </v-btn>
            </td>
          </tr>
        </tbody>
      </v-table>
    </v-card-text>
    
    <SalaryEntityDialog
      :editMode="true"
      v-model="dialogVisible"
      :path="`amount`"
      :entityId="selectedEntry?.id"
      :entityType="`SalaryEntry`"
      :entry="selectedEntry as any"
      formType="salary"
      :participantName="participantName"
      :type="ChangeType.Participant"
      :year="selectedEntry?.year"
      @refresh="fetchEntries"
    />
  </v-card>
</template>


<style scoped>
.salary-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

.salary-table th {
  font-size: 0.75rem;
  letter-spacing: 0.0625rem;
  font-weight: 500;
  background-color: #f5f5f5;
}

.bg-blue-lighten-5 {
  background-color: rgba(66, 165, 245, 0.1);
}
</style> 