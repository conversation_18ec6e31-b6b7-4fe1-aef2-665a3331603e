<script setup lang="ts">
  import { ref, computed } from 'vue'
  import PensionsCalcStrip from '@/components/participants/pensionCalculation/PensionsCalcStrip.vue'
  import PensionTableLayout from './PensionTableLayout.vue'
  import PensionTableRow from './PensionTableRow.vue'
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import { usePensionBase } from '@/composables/pension-base/usePensionBase'

  const pensionStore = usePensionStore();
  const {
    shouldShowCertified,
    hasCertifiedData,
    createExpansionState,
    getColumnYears,
    getStatusClass,
    formatCurrency,
    getColumnData
  } = usePensionBase();

  // Use the shared expansion state function
  const isPensionsPerReferenceExpanded = createExpansionState(true);

  // Use the shared column years function
  const { leftColumnYear, middleColumnYear, rightColumnYear } = getColumnYears();

  // Function to get column data with the specific data type for this component
  const getPensionReferenceData = (year: number | null) => {
    return getColumnData(year, 'certifiedPensionInfo');
  }
</script>

<template>
  <v-card-text class="py-0">
    <v-expansion-panels v-model="isPensionsPerReferenceExpanded" class="mt-6">
      <v-expansion-panel elevation="2">
        <v-expansion-panel-title class="py-3">
          <template v-slot:default="{ expanded }">
            <v-row no-gutters>
              <v-col cols="8" class="d-flex align-center">
                <h4 class="text-subtitle-1 font-weight-bold">Pensions as per reference date</h4>
              </v-col>
              <v-col cols="4" class="text-right">
                <v-icon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'"></v-icon>
              </v-col>
            </v-row>
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <PensionsCalcStrip />

          <PensionTableLayout>
            <template #header-label>DESCRIPTION</template>
            <template #header-left>
              <div :class="getStatusClass(leftColumnYear)">
                <v-chip v-if="shouldShowCertified(leftColumnYear, pensionStore.leftColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ leftColumnYear }}</div>
              </div>
            </template>
            <template #header-middle>
              <div :class="getStatusClass(middleColumnYear)">
                <v-chip v-if="shouldShowCertified(middleColumnYear, pensionStore.middleColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ middleColumnYear }}</div>
              </div>
            </template>
            <template #header-right>
              <div :class="getStatusClass(rightColumnYear)">
                <v-chip v-if="shouldShowCertified(rightColumnYear, pensionStore.rightColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ rightColumnYear }}</div>
              </div>
            </template>

            <PensionTableRow label="Code" :isAlternate="true">
              <template #left>{{ getPensionReferenceData(leftColumnYear)?.code ?? 'N/A' }}</template>
              <template #middle>{{ getPensionReferenceData(middleColumnYear)?.code ?? 'N/A' }}</template>
              <template #right>{{ getPensionReferenceData(rightColumnYear)?.code ?? 'N/A' }}</template>
            </PensionTableRow>

            <PensionTableRow label="OP-TE">
              <template #left>{{ formatCurrency(getPensionReferenceData(leftColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
              <template #middle>{{ formatCurrency(getPensionReferenceData(middleColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
              <template #right>{{ formatCurrency(getPensionReferenceData(rightColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="OP-TB" :isAlternate="true">
              <template #left>{{ formatCurrency(getPensionReferenceData(leftColumnYear)?.attainableGrossAnnualOldAgePension) }}</template>
              <template #middle>{{ formatCurrency(getPensionReferenceData(middleColumnYear)?.attainableGrossAnnualOldAgePension) }}</template>
              <template #right>{{ formatCurrency(getPensionReferenceData(rightColumnYear)?.attainableGrossAnnualOldAgePension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="WP-TE">
              <template #left>{{ formatCurrency(getPensionReferenceData(leftColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
              <template #middle>{{ formatCurrency(getPensionReferenceData(middleColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
              <template #right>{{ formatCurrency(getPensionReferenceData(rightColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="ONP-TE" :isAlternate="true">
              <template #left>{{ formatCurrency(getPensionReferenceData(leftColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
              <template #middle>{{ formatCurrency(getPensionReferenceData(middleColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
              <template #right>{{ formatCurrency(getPensionReferenceData(rightColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="AOP-TE">
              <template #left>{{ formatCurrency(getPensionReferenceData(leftColumnYear)?.grossAnnualDisabilityPension) }}</template>
              <template #middle>{{ formatCurrency(getPensionReferenceData(middleColumnYear)?.grossAnnualDisabilityPension) }}</template>
              <template #right>{{ formatCurrency(getPensionReferenceData(rightColumnYear)?.grossAnnualDisabilityPension) }}</template>
            </PensionTableRow>
          </PensionTableLayout>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card-text>
</template>

<style scoped>
  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>