<script setup lang="ts">
  import { ref } from 'vue'
  import PensionsCalcStrip from '@/components/participants/pensionCalculation/PensionsCalcStrip.vue'
  import PensionTableLayout from './PensionTableLayout.vue'
  import PensionTableRow from './PensionTableRow.vue'
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import { usePensionBase } from '@/composables/pension-base/usePensionBase'

  const pensionStore = usePensionStore();
  const {
    shouldShowCertified,
    createExpansionState,
    getColumnYears,
    getStatusClass,
    formatCurrency,
    getColumnData
  } = usePensionBase();

  const isIndexationExpanded = createExpansionState(true);

  const { leftColumnYear, middleColumnYear, rightColumnYear } = getColumnYears();

  const getIndexationData = (year: number | null) => {
    return getColumnData(year, 'certifiedIndexationStartOfYear');
  }
</script>

<template>
  <v-card-text class="py-0">
    <v-expansion-panels v-model="isIndexationExpanded">
      <v-expansion-panel elevation="2">
        <v-expansion-panel-title class="py-3">
          <template v-slot:default="{ expanded }">
            <v-row no-gutters>
              <v-col cols="8" class="d-flex align-center">
                <h4 class="text-subtitle-1 font-weight-bold">Indexation beginning of calendar year</h4>
              </v-col>
              <v-col cols="4" class="text-right">
                <v-icon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'"></v-icon>
              </v-col>
            </v-row>
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <PensionsCalcStrip :dateSelectionDisabled="true" />

          <PensionTableLayout>
            <template #header-label>DESCRIPTION</template>
            <template #header-left>
              <div :class="getStatusClass(leftColumnYear)">
                <v-chip v-if="shouldShowCertified(leftColumnYear, pensionStore.leftColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ leftColumnYear }}</div>
              </div>
            </template>
            <template #header-middle>
              <div :class="getStatusClass(middleColumnYear)">
                <v-chip v-if="shouldShowCertified(middleColumnYear, pensionStore.middleColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ middleColumnYear }}</div>
              </div>
            </template>
            <template #header-right>
              <div :class="getStatusClass(rightColumnYear)">
                <v-chip v-if="shouldShowCertified(rightColumnYear, pensionStore.rightColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ rightColumnYear }}</div>
              </div>
            </template>

            <PensionTableRow label="OP-TE" :isAlternate="true">
              <template #left>{{ formatCurrency(getIndexationData(leftColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
              <template #middle>{{ formatCurrency(getIndexationData(middleColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
              <template #right>{{ formatCurrency(getIndexationData(rightColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="WP-TE">
              <template #left>{{ formatCurrency(getIndexationData(leftColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
              <template #middle>{{ formatCurrency(getIndexationData(middleColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
              <template #right>{{ formatCurrency(getIndexationData(rightColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="ONP-TE" :isAlternate="true">
              <template #left>{{ formatCurrency(getIndexationData(leftColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
              <template #middle>{{ formatCurrency(getIndexationData(middleColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
              <template #right>{{ formatCurrency(getIndexationData(rightColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="AOP-TE">
              <template #left>{{ formatCurrency(getIndexationData(leftColumnYear)?.grossAnnualDisabilityPension) }}</template>
              <template #middle>{{ formatCurrency(getIndexationData(middleColumnYear)?.grossAnnualDisabilityPension) }}</template>
              <template #right>{{ formatCurrency(getIndexationData(rightColumnYear)?.grossAnnualDisabilityPension) }}</template>
            </PensionTableRow>
          </PensionTableLayout>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card-text>
</template>

<style scoped>
  .v-expansion-panel-title {
    min-height: 48px;
  }

  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>