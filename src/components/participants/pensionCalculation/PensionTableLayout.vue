<template>
  <v-table class="pension-table">
    <thead>
      <tr>
        <th class="text-left" style="width: 30%;">
          <slot name="header-label">DESCRIPTION</slot>
        </th>
        <th class="text-right" style="width: 23.33%;">
          <slot name="header-left"></slot>
        </th>
        <th class="text-right" style="width: 23.33%;">
          <slot name="header-middle"></slot>
        </th>
        <th class="text-right" style="width: 23.33%;">
          <slot name="header-right"></slot>
        </th>
      </tr>
    </thead>
    <tbody>
      <slot></slot>
    </tbody>
  </v-table>
</template>

<style scoped>
.pension-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  table-layout: fixed;
  width: 100%;
}

.pension-table th {
  font-weight: 500;
  background-color: #f5f5f5;
}

/* Add padding to the right-aligned cells for better readability */
.pension-table th.text-right,
.pension-table td.text-right {
  padding-right: 16px;
}
</style>
