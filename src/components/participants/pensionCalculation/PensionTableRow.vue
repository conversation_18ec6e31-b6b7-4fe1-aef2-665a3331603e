<script setup lang="ts">
defineProps({
  label: {
    type: String,
    required: true
  },
  isAlternate: {
    type: Boolean,
    default: false
  }
})
</script>

<template>
  <tr :class="{ 'bg-grey-lighten-5': isAlternate }">
    <td class="font-weight-medium">{{ label }}</td>
    <td class="text-right">
      <slot name="left"></slot>
    </td>
    <td class="text-right">
      <slot name="middle"></slot>
    </td>
    <td class="text-right">
      <slot name="right"></slot>
    </td>
  </tr>
</template>
