<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import { usePensionParameters } from '@/composables/pension-parameters/usePensionParameters'
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { SplitDate } from '@/types/participant.types'
  import { usePensionCalculations } from '@/composables/pension-calculations/usePensionCalculations'
  import { safeCalculate } from '@/utils/transformers'
  import { usePensionBase } from '@/composables/pension-base/usePensionBase'
  import PensionTableLayout from './PensionTableLayout.vue'
  import PensionTableRow from './PensionTableRow.vue'

  const pensionStore = usePensionStore();
  const {
    hasCertifiedData,
    shouldShowCertified,
    createExpansionState,
    getColumnYears,
    getStatusClass
  } = usePensionBase()

  // Use the shared expansion state function
  const isAccrualExpanded = createExpansionState(0);

  const {state:{ normalizePensionParams}} = usePensionParameters()
  const {state:{ normalizedParticipantSalaryEntries}} = useParticipants()

  const {actions:{calculateAccrualPeriodToReferenceDate, calculateAccrualPeriodAfterReferenceDate}} = usePensionCalculations()

  const normalizedParticipantCertifiedData = computed(()=>{
    return pensionStore.normalizedCertifiedData;
  })

  const participantPersonalInfo = computed(() => pensionStore.activeParticipant.personalInfo)

  const accrualPeriodData = computed(() => {
    // Helper function to get participant info for a given column year
    const getParticipantInfo = (year: number, columnDate: Date) => {
      if (!year || !normalizedParticipantSalaryEntries.value || !normalizePensionParams.value) {
        return {
          startDate: 'Dec 31, 2019',
          endDate: 'May 11, 2025',
          birthDate: 'Jul 31, 1982',
          aovAge: 65,
          retirementDate: 'Jul 31, 2047',
          periodDuringYear: 1.00,
          periodAfterReferenceDate: 22.60
        };
      }

    // const employmentInfo = computed(() =>
    //       hasCertifiedData(year)
    //         ? normalizedParticipantCertifiedData.value[year]?.certifiedEmploymentInfo
    //         : pensionStore.activeParticipant.employmentInfo
    //     );

      //TODO::Update to use certified data if available
      const employmentInfo = computed(() =>
          hasCertifiedData(year)
            ? pensionStore.activeParticipant.employmentInfo
            : pensionStore.activeParticipant.employmentInfo
        );

      const byear = participantPersonalInfo.value.birthYear;
      const bmonth = participantPersonalInfo.value.birthMonth;
      const bday = participantPersonalInfo.value.birthDay;

      return {
        startDate: formatDate(employmentInfo.value?.startDate) ,
        endDate: employmentInfo.value?.endDate || '-',
        birthDate: formatBirthDate(participantPersonalInfo.value),
        aovAge: getAovAge(year) || 65,
        retirementDate: calculateRetirementDate(participantPersonalInfo.value, getAovAge(year)),
        periodDuringYear: calculatePeriodDuringYear(year, splitDate(employmentInfo.value?.startDate || ''), splitDate(columnDate as Date || new Date())),
        periodAfterReferenceDate: calculatePeriodAfterReferenceDate({ year: byear, month: bmonth, day: bday }, getAovAge(year), splitDate(columnDate as Date || new Date()))
      };
    };

    // Get parameters for each column
    const leftColumnInfo = getParticipantInfo(pensionStore.leftColumnYear as number, pensionStore.leftColumnDate as Date);
    const middleColumnInfo = getParticipantInfo(pensionStore.middleColumnYear as number, pensionStore.middleColumnDate as Date);
    const rightColumnInfo = getParticipantInfo(pensionStore.rightColumnYear as number, pensionStore.rightColumnDate as Date);

    return {
      leftColumn: {
        year: pensionStore.leftColumnYear || 2023,
        startDate: leftColumnInfo.startDate,
        endDate: leftColumnInfo.endDate || '-',
        birthDate: leftColumnInfo.birthDate,
        aovAge: leftColumnInfo.aovAge,
        retirementDate: leftColumnInfo.retirementDate,
        periodDuringYear: leftColumnInfo.periodDuringYear,
        periodAfterReferenceDate: leftColumnInfo.periodAfterReferenceDate
      },
      middleColumn: {
        year: pensionStore.middleColumnYear || 2024,
        startDate: middleColumnInfo.startDate,
        endDate: middleColumnInfo.endDate,
        birthDate: middleColumnInfo.birthDate,
        aovAge: middleColumnInfo.aovAge,
        retirementDate: middleColumnInfo.retirementDate,
        periodDuringYear: middleColumnInfo.periodDuringYear,
        periodAfterReferenceDate: middleColumnInfo.periodAfterReferenceDate
      },
      rightColumn: {
        year: pensionStore.rightColumnYear || 2025,
        startDate: rightColumnInfo.startDate,
        endDate: rightColumnInfo.endDate,
        birthDate: rightColumnInfo.birthDate,
        aovAge: rightColumnInfo.aovAge,
        retirementDate: rightColumnInfo.retirementDate,
        periodDuringYear: rightColumnInfo.periodDuringYear,
        periodAfterReferenceDate: rightColumnInfo.periodAfterReferenceDate
      }
    };
  });

  // Utility functions
  const formatBirthDate = (personalInfo: any) => {
    if (!personalInfo) return '';

    const month = personalInfo.birthMonth || 0;
    const monthName = getMonthName(month);
    const day = personalInfo.birthDay || 0;
    const year = personalInfo.birthYear || 0;

    return `${monthName} ${day}, ${year}`;
  };

  const formatDate = (date: string) => {
    if (!date) return '-';

    const parsedDate = new Date(date);
    const day = parsedDate.getDate();
    const month = getMonthName(parsedDate.getMonth() + 1);
    const year = parsedDate.getFullYear();

    return `${month} ${day}, ${year}`;
  };

  const getMonthName = (month: number) => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[(month - 1) % 12];
  };

  const getAovAge = (year: number) => {
    if (normalizePensionParams.value && normalizePensionParams.value[year]) {
      return normalizePensionParams.value[year].retirementAge || 65;
    }
    return 65;
  };

  const calculateRetirementDate = (personalInfo: any, aovAge: number) => {
    if (!personalInfo) return 'Jul 31, 2047';

    const birthYear = personalInfo.birthYear;
    const birthMonth = personalInfo.birthMonth;
    const birthDay = personalInfo.birthDay;

    const retirementYear = birthYear + aovAge;
    const monthName = 'Dec';
    const retirementDay = 1;

    return `${monthName} ${retirementDay}, ${retirementYear}`;
  };

  const calculatePeriodDuringYear = (year: number, startDate: SplitDate, columnDate: SplitDate  ) => {
    return safeCalculate(
      calculateAccrualPeriodToReferenceDate,
      startDate,
      year,
      columnDate
    );
  };

  // const leftOpteAccrualAfterReferenceDate = safeCalculate(
  //   calculateAccrualPeriodAfterReferenceDate,
  //   participantDateOfBirth.value,
  //   leftColumnParams?.retirementAge || 65,
  //   splitDate(pensionStore.leftColumnDate as Date || new Date()),
  // );

  const calculatePeriodAfterReferenceDate = (dateOfBirth: SplitDate, retirementAge: number, columnDate: SplitDate) => {
    // This would calculate the period from reference date to retirement
    return safeCalculate(
      calculateAccrualPeriodAfterReferenceDate,
      dateOfBirth,
      retirementAge,
      columnDate
    );
  };

  const getStatusText = (year: number) => {
    return hasCertifiedData(year) ? 'CERTIFIED' : 'CERTIFICATION NOT STARTED';
  };
</script>

<template>
  <v-card class="pension-calculation-container" elevation="0">
    <v-card-text>
      <!-- Accrual Period Section -->
      <v-expansion-panels v-model="isAccrualExpanded">
        <v-expansion-panel elevation="2">
          <v-expansion-panel-title class="py-3">
            <template v-slot:default="{ expanded }">
              <v-row no-gutters>
                <v-col cols="8" class="d-flex align-center">
                  <h4 class="text-subtitle-1 font-weight-bold">Accrual period calculation</h4>
                </v-col>
                <v-col cols="4" class="text-right">
                  <v-icon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'"></v-icon>
                </v-col>
              </v-row>
            </template>
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <!-- Year Selection Row Strip -->
            <PensionsCalcStrip />


            <!-- Table -->
            <PensionTableLayout>
              <template #header-label>DESCRIPTION</template>
              <template #header-left>
                <div :class="getStatusClass(accrualPeriodData.leftColumn.year)">
                  <v-chip v-if="shouldShowCertified(accrualPeriodData.leftColumn.year, pensionStore.leftColumnDate)" color="success" size="small">
                    Certified
                  </v-chip>
                  <v-chip v-else color="warning" size="small">
                    Certification pending
                  </v-chip>
                  <div class="text-caption">{{ accrualPeriodData.leftColumn.year }}</div>
                </div>
              </template>
              <template #header-middle>
                <div :class="getStatusClass(accrualPeriodData.middleColumn.year)">
                  <v-chip v-if="shouldShowCertified(accrualPeriodData.middleColumn.year, pensionStore.middleColumnDate)" color="success" size="small">
                    Certified
                  </v-chip>
                  <v-chip v-else color="warning" size="small">
                    Certification pending
                  </v-chip>
                  <div class="text-caption">{{ accrualPeriodData.middleColumn.year }}</div>
                </div>
              </template>
              <template #header-right>
                <div>{{ accrualPeriodData.rightColumn.year }}</div>
              </template>

              <PensionTableRow label="Start date of participation" :isAlternate="true">
                <template #left>{{ accrualPeriodData.leftColumn.startDate }}</template>
                <template #middle>{{ accrualPeriodData.middleColumn.startDate }}</template>
                <template #right>{{ accrualPeriodData.rightColumn.startDate }}</template>
              </PensionTableRow>

              <PensionTableRow label="End date of employment">
                <template #left>{{ formatDate(accrualPeriodData.leftColumn.endDate) }}</template>
                <template #middle>{{ formatDate(accrualPeriodData.middleColumn.endDate) }}</template>
                <template #right>{{ formatDate(accrualPeriodData.rightColumn.endDate) }}</template>
              </PensionTableRow>

              <PensionTableRow label="Birth date" :isAlternate="true">
                <template #left>{{ accrualPeriodData.leftColumn.birthDate }}</template>
                <template #middle>{{ accrualPeriodData.middleColumn.birthDate }}</template>
                <template #right>{{ accrualPeriodData.rightColumn.birthDate }}</template>
              </PensionTableRow>

              <PensionTableRow label="AOV age">
                <template #left>{{ accrualPeriodData.leftColumn.aovAge }}</template>
                <template #middle>{{ accrualPeriodData.middleColumn.aovAge }}</template>
                <template #right>{{ accrualPeriodData.rightColumn.aovAge }}</template>
              </PensionTableRow>

              <PensionTableRow label="Retirement date" :isAlternate="true">
                <template #left>{{ accrualPeriodData.leftColumn.retirementDate }}</template>
                <template #middle>{{ accrualPeriodData.middleColumn.retirementDate }}</template>
                <template #right>{{ accrualPeriodData.rightColumn.retirementDate }}</template>
              </PensionTableRow>

              <PensionTableRow label="Period during calculation year">
                <template #left>{{ accrualPeriodData.leftColumn.periodDuringYear.toFixed(2) }}</template>
                <template #middle>{{ accrualPeriodData.middleColumn.periodDuringYear.toFixed(2) }}</template>
                <template #right>{{ accrualPeriodData.rightColumn.periodDuringYear.toFixed(2) }}</template>
              </PensionTableRow>

              <PensionTableRow label="Period after reference date" :isAlternate="true">
                <template #left>{{ accrualPeriodData.leftColumn.periodAfterReferenceDate.toFixed(2) }}</template>
                <template #middle>{{ accrualPeriodData.middleColumn.periodAfterReferenceDate.toFixed(2) }}</template>
                <template #right>{{ accrualPeriodData.rightColumn.periodAfterReferenceDate.toFixed(2) }}</template>
              </PensionTableRow>
            </PensionTableLayout>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-card-text>
  </v-card>
</template>

<style scoped>
  .pension-calculation-container {
    border-radius: 8px;
  }

  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>