<script setup lang="ts">
  import PensionsCalcStrip from '@/components/participants/pensionCalculation/PensionsCalcStrip.vue';
  import PensionTableLayout from './PensionTableLayout.vue';
  import PensionTableRow from './PensionTableRow.vue';
  import EditCorrectionsDialog from '@/components/EditCorrectionsDialog.vue';
  import { usePensionStore } from '@/stores/pension/pensionStore';
  import { usePensionBase } from '@/composables/pension-base/usePensionBase';
  import { useCertifiedData } from '@/composables/certified-data';
  import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
  import { useParticipants } from '@/composables/participants/useParticipants'

  const isAfterCorrectionsExpanded = ref(true);
  const pensionStore = usePensionStore();
  const { shouldShowCertified, hasCertifiedData } = usePensionBase();
  const { state: { normalizedParticipantCertifiedData } } = useCertifiedData();
  const {actions:{refetchSingleParticipant}} = useParticipants()

  const {state:{participantChangeProposalsList}} = useChangeProposal()

  const currentParticipantData = computed(() => pensionStore.activeParticipant);

  const pensionChangesWithCorrections = computed(() => {
    if (!participantChangeProposalsList.value?.length) return {};

    return participantChangeProposalsList.value
      .filter(change => change.rawData.proposal.entityType === 'PensionInfo')
      .reduce((acc, change) => {
        const field = change.field;
        const newValue = parseFloat(change.newValue);
        const currentValue = parseFloat(change.currentValue);

        acc[field] = {
          newValue,
          currentValue,
          correction: newValue - currentValue
        };

        return acc;
      }, {} as Record<string, { newValue: number; currentValue: number; correction: number }>);
  });

  const leftColumnYear = computed(() => pensionStore.leftColumnYear);
  const rightColumnYear = computed(() => pensionStore.rightColumnYear);
  const middleColumnYear = computed(() => pensionStore.middleColumnYear);

  const getColumnData = (year: number | null) => {
    if (!year) return null;
    //TODO::Update to use certified data if available
    // if(hasCertifiedData(year)){
    //   return normalizedParticipantCertifiedData.value?.[year]?.certifiedPensionCorrections;
    // }else{
      return currentParticipantData.value?.pensionInfo;
    // }
  };

  const getStatusClass = (year: number | null) => {
    if (!year) return null;
    if (pensionStore.certifiedDataYears?.includes(year)) return 'bg-green-lighten-5 text-green-darken-2';
    return 'bg-orange-lighten-5 text-orange-darken-2';
  };

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined) return '-';
    return new Intl.NumberFormat('nl-AW', {
      style: 'currency',
      currency: 'AWG',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const editDialog = ref(false);
  const selectedField = ref({
    field: '',
    value:  0,
    year: 0,
    entityId: ''
  });

  const openEditDialog = (field: string, value: number , year: number, entityId: string) => {
    selectedField.value = {
      field,
      value,
      year,
      entityId
    };
    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const updateField = (newValue: number) => {
    // TODO: Implement the actual update logic here, likely involving an API call
    console.log('Updating', selectedField.value.field, 'for year', selectedField.value.year, 'with', newValue);
    closeEditDialog();
  };

  const handleRefresh = async () => {
    await refetchSingleParticipant();
  };

  const hasPendingChanges = (pendingChanges: string[], field: string) => {
    if(!pendingChanges) return false;
    return pendingChanges.includes(field)
  };

</script>

<template>
  <v-card-text class="py-0">
    <v-expansion-panels v-model="isAfterCorrectionsExpanded" class="mb-6">
      <v-expansion-panel elevation="2">
        <v-expansion-panel-title class="py-3">
          <template v-slot:default="{ expanded }">
            <v-row no-gutters>
              <v-col cols="8" class="d-flex align-center">
                <h4 class="text-subtitle-1 font-weight-bold">Pensions after corrections (start of calendar year before indexation)</h4>
              </v-col>
              <v-col cols="4" class="text-right">
                <v-icon :icon="expanded ? 'mdi-chevron-up' : 'mdi-chevron-down'"></v-icon>
              </v-col>
            </v-row>
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <PensionsCalcStrip :dateSelectionDisabled="true" />

          <PensionTableLayout>
            <template #header-label>DESCRIPTION</template>
            <template #header-left>
              <div :class="getStatusClass(leftColumnYear)">
                <v-chip v-if="shouldShowCertified(leftColumnYear, pensionStore.leftColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ leftColumnYear }}</div>
              </div>
            </template>
            <template #header-middle>
              <div :class="getStatusClass(middleColumnYear)">
                <v-chip v-if="shouldShowCertified(middleColumnYear, pensionStore.middleColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ middleColumnYear }}</div>
              </div>
            </template>
            <template #header-right>
              <div :class="getStatusClass(rightColumnYear)">
                <v-chip v-if="shouldShowCertified(rightColumnYear, pensionStore.rightColumnDate)" color="success" size="small">
                  Certified
                </v-chip>
                <v-chip v-else color="warning" size="small">
                  Certification pending
                </v-chip>
                <div class="text-caption">{{ rightColumnYear }}</div>
              </div>
            </template>

            <PensionTableRow label="OP-TE" :isAlternate="true">
              <template #left>{{ formatCurrency(getColumnData(leftColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
              <template #middle>
                {{ formatCurrency(getColumnData(middleColumnYear)?.accruedGrossAnnualOldAgePension) }}
                <v-chip v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'accruedGrossAnnualOldAgePension')" color="primary" size="small">{{formatCurrency(pensionChangesWithCorrections['accruedGrossAnnualOldAgePension']?.correction)}}
                </v-chip>
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'accruedGrossAnnualOldAgePension')">
                  <VIcon
                    size="16"
                    icon="tabler-alert-triangle"
                    class="edit-icon"
                    color="error"
                  />
                </VBtn>
                <VBtn
                  v-else
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  @click="openEditDialog('accruedGrossAnnualOldAgePension', getColumnData(middleColumnYear)?.accruedGrossAnnualOldAgePension, middleColumnYear as number, getColumnData(middleColumnYear)?.id)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-edit"
                    class="edit-icon"
                    color="primary"
                  />
                </VBtn>
              </template>
              <template #right>{{ formatCurrency(getColumnData(rightColumnYear)?.accruedGrossAnnualOldAgePension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="OP-TB">
              <template #left>{{ formatCurrency(getColumnData(leftColumnYear)?.attainableGrossAnnualOldAgePension) }}</template>
              <template #middle>
                {{ formatCurrency(getColumnData(middleColumnYear)?.attainableGrossAnnualOldAgePension) }}
                <v-chip v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'attainableGrossAnnualOldAgePension')" color="primary" size="small">
                  {{formatCurrency(pensionChangesWithCorrections['attainableGrossAnnualOldAgePension']?.correction)}}
                </v-chip>
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'attainableGrossAnnualOldAgePension')">
                  <VIcon
                    size="16"
                    icon="tabler-alert-triangle"
                    class="edit-icon"
                    color="error"
                  />
                </VBtn>
                <VBtn
                  v-else
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  @click="openEditDialog('attainableGrossAnnualOldAgePension', getColumnData(middleColumnYear)?.attainableGrossAnnualOldAgePension, middleColumnYear as number, getColumnData(middleColumnYear)?.id)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-edit"
                    class="edit-icon"
                    color="primary"
                  />
                </VBtn>
              </template>
              <template #right>{{ formatCurrency(getColumnData(rightColumnYear)?.attainableGrossAnnualOldAgePension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="WP-TE" :isAlternate="true">
              <template #left>{{ formatCurrency(getColumnData(leftColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
              <template #middle>
                {{ formatCurrency(getColumnData(middleColumnYear)?.accruedGrossAnnualPartnersPension) }}
                <v-chip v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'accruedGrossAnnualPartnersPension')" color="primary" size="small">
                  {{formatCurrency(pensionChangesWithCorrections['accruedGrossAnnualPartnersPension']?.correction)}}
                </v-chip>
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'accruedGrossAnnualPartnersPension')">
                  <VIcon
                    size="16"
                    icon="tabler-alert-triangle"
                    class="edit-icon"
                    color="error"
                  />
                </VBtn>
                <VBtn
                  v-else
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  @click="openEditDialog('accruedGrossAnnualPartnersPension', getColumnData(middleColumnYear)?.accruedGrossAnnualPartnersPension, middleColumnYear as number, getColumnData(middleColumnYear)?.id)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-edit"
                    class="edit-icon"
                    color="primary"
                  />
                </VBtn>
              </template>
              <template #right>{{ formatCurrency(getColumnData(rightColumnYear)?.accruedGrossAnnualPartnersPension) }}</template>
            </PensionTableRow>

            <PensionTableRow label="ONP-TE">
              <template #left>{{ formatCurrency(getColumnData(leftColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
              <template #middle>
                {{ formatCurrency(getColumnData(middleColumnYear)?.accruedGrossAnnualSinglesPension) }}
                <v-chip v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'accruedGrossAnnualSinglesPension')" color="primary" size="small">
                  {{formatCurrency(pensionChangesWithCorrections['accruedGrossAnnualSinglesPension']?.correction)}}
                </v-chip>
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  v-if="hasPendingChanges(getColumnData(middleColumnYear)?.pendingChanges, 'accruedGrossAnnualSinglesPension')">
                  <VIcon
                    size="16"
                    icon="tabler-alert-triangle"
                    class="edit-icon"
                    color="error"
                  />
                </VBtn>
                <VBtn
                  v-else
                  icon
                  size="small"
                  variant="text"
                  color="primary"
                  @click="openEditDialog('accruedGrossAnnualSinglesPension', getColumnData(middleColumnYear)?.accruedGrossAnnualSinglesPension, middleColumnYear as number, getColumnData(middleColumnYear)?.id)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-edit"
                    class="edit-icon"
                    color="primary"
                  />
                </VBtn>
              </template>
              <template #right>{{ formatCurrency(getColumnData(rightColumnYear)?.accruedGrossAnnualSinglesPension) }}</template>
            </PensionTableRow>
          </PensionTableLayout>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <EditCorrectionsDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :field-name="selectedField.field"
      :currentValue="selectedField.value.toString()"
      :entity-id="selectedField?.entityId"
      entity-type="PensionInfo"
      type="PARTICIPANT"
      :year="selectedField.year"
      @close="closeEditDialog"
      @update="updateField"
      @refresh="handleRefresh"
    />
  </v-card-text>
</template>

<style scoped>
  .v-expansion-panel-title {
    min-height: 48px;
  }

  .v-expansion-panel-title h4 {
    margin: 0;
  }
</style>