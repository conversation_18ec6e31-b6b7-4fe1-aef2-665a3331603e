<script setup lang="ts">
  import { ref, computed, watch } from 'vue';

  const props = defineProps({
    participantsList: {
      type: Array,
      required: true
    }
  });

  const emit = defineEmits(['update:filters']);

  const isFilterPanelOpen = ref(false);
  const loadingFilteredParticipants = ref(false);

  // Generate filter options from participants list
  const filterOptions = computed(() => {
    const departments = new Set();
    const positions = new Set();
    const locations = new Set();
    const statuses = new Set();

    props.participantsList.forEach(item => {
      if (item.department) departments.add(item.department);
      if (item.position) positions.add(item.position);
      if (item.location) locations.add(item.location);
      if (item.status) statuses.add(item.status);
    });

    return {
      departments: Array.from(departments).map(item => ({ id: item, text: item })),
      positions: Array.from(positions).map(item => ({ id: item, text: item })),
      locations: Array.from(locations).map(item => ({ id: item, text: item })),
      statuses: Array.from(statuses).map((item: any) => ({
        id: item,
        text: item.charAt(0).toUpperCase() + item.slice(1)
      }))
    };
  });

  // Filter state
  const filters = ref({
    department: null,
    position: null,
    status: null,
    location: null,
    birthDateRange: {
      startDate: null,
      endDate: null
    },
    enrollmentDateRange: {
      startDate: null,
      endDate: null
    }
  });

  // Date range menu states
  const birthDateRangeMenu = ref(false);
  const enrollmentDateRangeMenu = ref(false);

  // Computed properties for date range displays
  const birthDateRangeDisplay = computed(() => {
    const { startDate, endDate } = filters.value.birthDateRange;
    if (startDate && endDate) {
      return `${formatDateToString(startDate)} - ${formatDateToString(endDate)}`;
    }
    return 'Select Birth Date Range';
  });

  const enrollmentDateRangeDisplay = computed(() => {
    const { startDate, endDate } = filters.value.enrollmentDateRange;
    if (startDate && endDate) {
      return `${formatDateToString(startDate)} - ${formatDateToString(endDate)}`;
    }
    return 'Select Enrollment Date Range';
  });

  // Method to clear all filters
  const resetFilters = () => {
    filters.value = {
      department: null,
      position: null,
      status: null,
      location: null,
      birthDateRange: {
        startDate: null,
        endDate: null
      },
      enrollmentDateRange: {
        startDate: null,
        endDate: null
      }
    };
    birthDateRangeMenu.value = false;
    enrollmentDateRangeMenu.value = false;
    emit('update:filters', {});
  };

  // Clear date range filters
  const clearBirthDateRangeFilter = () => {
    filters.value.birthDateRange.startDate = null;
    filters.value.birthDateRange.endDate = null;
    birthDateRangeMenu.value = false;
  };

  const clearEnrollmentDateRangeFilter = () => {
    filters.value.enrollmentDateRange.startDate = null;
    filters.value.enrollmentDateRange.endDate = null;
    enrollmentDateRangeMenu.value = false;
  };

  // Toggle filter panel
  const toggleFilterPanel = () => {
    isFilterPanelOpen.value = !isFilterPanelOpen.value;
  };

  // Apply filters
  const applyFilters = () => {
    loadingFilteredParticipants.value = true;

    // Prepare filters for emitting
    const preparedFilters = {
      status: filters.value.status?.id || null,
      birthDateRange: {
        startDate: filters.value.birthDateRange.startDate,
        endDate: filters.value.birthDateRange.endDate
      },
      enrollmentDateRange: {
        startDate: filters.value.enrollmentDateRange.startDate,
        endDate: filters.value.enrollmentDateRange.endDate
      }
    };

    emit('update:filters', preparedFilters);

    // Simulate loading
    setTimeout(() => {
      loadingFilteredParticipants.value = false;
    }, 500);
  };

  // Count applied filters
  const appliedFiltersCount = computed(() => {
    let count = 0;
    if (filters.value.status) count++;
    if (filters.value.birthDateRange.startDate && filters.value.birthDateRange.endDate) count++;
    return count;
  });
</script>

<template>
  <div class="filter-component mb-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <v-btn
        prepend-icon="tabler-filter"
        color="primary"
        size="small"
        variant="outlined"
        @click="toggleFilterPanel"
        class="filter-btn"
      >
        Advanced Filter
        <template v-slot:append>
          <v-badge
            v-if="appliedFiltersCount > 0"
            color="error"
            :content="appliedFiltersCount"
            inline
          ></v-badge>
        </template>
      </v-btn>
    </div>

    <v-expand-transition>
      <v-card variant="outlined" v-if="isFilterPanelOpen" class="mb-4">
        <v-card-text>
          <v-row>

            <v-col cols="12" sm="6" md="4">
              <v-select
                :disabled="loadingFilteredParticipants"
                v-model="filters.status"
                :items="filterOptions.statuses"
                item-title="text"
                item-value="id"
                label="Status"
                prepend-inner-icon="tabler-circle-check"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-select
                :disabled="loadingFilteredParticipants"
                v-model="filters.location"
                :items="filterOptions.locations"
                item-title="text"
                item-value="id"
                label="Location"
                prepend-inner-icon="tabler-map-pin"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-menu
                :disabled="loadingFilteredParticipants"
                v-model="birthDateRangeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-bind="props"
                    v-model="birthDateRangeDisplay"
                    label="Birth Date Range"
                    prepend-inner-icon="tabler-calendar"
                    readonly
                    density="compact"
                    variant="outlined"
                    clearable
                    @click:clear="clearBirthDateRangeFilter"
                  ></v-text-field>
                </template>

                <v-card min-width="300px">
                  <v-card-text>
                    <v-row>
                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredParticipants"
                          v-model="filters.birthDateRange.startDate"
                          label="Start Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>

                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredParticipants"
                          v-model="filters.birthDateRange.endDate"
                          label="End Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn @click="clearBirthDateRangeFilter" color="primary">
                      Clear
                    </v-btn>
                    <v-btn color="success" @click="birthDateRangeMenu = false">
                      Apply
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-menu>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-menu
                :disabled="loadingFilteredParticipants"
                v-model="enrollmentDateRangeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-bind="props"
                    v-model="enrollmentDateRangeDisplay"
                    label="Enrollment Date Range"
                    prepend-inner-icon="tabler-calendar"
                    readonly
                    density="compact"
                    variant="outlined"
                    clearable
                    @click:clear="clearEnrollmentDateRangeFilter"
                  ></v-text-field>
                </template>

                <v-card min-width="300px">
                  <v-card-text>
                    <v-row>
                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredParticipants"
                          v-model="filters.enrollmentDateRange.startDate"
                          label="Start Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>

                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredParticipants"
                          v-model="filters.enrollmentDateRange.endDate"
                          label="End Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn @click="clearEnrollmentDateRangeFilter" color="primary">
                      Clear
                    </v-btn>
                    <v-btn color="success" @click="enrollmentDateRangeMenu = false">
                      Apply
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-menu>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            size="small"
            prepend-icon="tabler-x"
            @click="resetFilters"
            class="clear-btn mx-4"
          >
            Clear All
          </v-btn>
          <v-btn
            :loading="loadingFilteredParticipants"
            :disabled="appliedFiltersCount === 0 || loadingFilteredParticipants"
            variant="outlined"
            color="primary"
            @click="applyFilters"
          >
            Apply Filters
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-expand-transition>
  </div>
</template>

<style scoped>
  .filter-component {
    width: 100%;
  }
</style>