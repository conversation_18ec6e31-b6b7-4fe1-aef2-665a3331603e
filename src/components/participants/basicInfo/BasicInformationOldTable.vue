<script setup lang="ts">

  const props = defineProps({
    information: {
      type: Array,
      default: () => [
        { id: 'eba340d0-8a24-48bc-957d-8362da88dc01', field: 'First Name', value: 'Mandatos' },
        { id: 'eba340d0-8a24-48bc-957d-8362da88dc02', field: 'Last Name', value: '<PERSON><PERSON><PERSON>' },
        { id: 'eba340d0-8a24-48bc-957d-8362da88dc03', field: 'Date of Birth', value: 'Aug 1, 1982' },
        { id: 'eba340d0-8a24-48bc-957d-8362da88dc04', field: 'Sex', value: 'M' },
        { id: 'eba340d0-8a24-48bc-957d-8362da88dc05', field: 'Email', value: '<EMAIL>' },
        { id: 'eba340d0-8a24-48bc-957d-8362da88dc06', field: 'Phone', value: '+31612345678' },
      ]
    }
  });

  const emit = defineEmits(['update:information']);

  const editDialog = ref(false);
  const selectedField = ref({ field: '', value: '', id: '' });

  const openEditDialog = (item: any) => {
    selectedField.value = item;
    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const updateField = (updatedValue: string) => {
    const fieldIndex = props.information.findIndex(
      field => field.id === selectedField.value.id
    );

    if (fieldIndex !== -1) {
      const updatedInformation = [...props.information];
      updatedInformation[fieldIndex].value = updatedValue;
      emit('update:information', updatedInformation);
    }

    closeEditDialog();
  };

  const isCollapsed = ref(false);
  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
  };
</script>

<template>
  <div class="basic-information-container">
    <div class="header-container">
      <h2>Basic Information</h2>
      <VBtn
        variant="outlined"
        @click="toggleCollapse"
      >
        Collapse
        <v-icon>{{ isCollapsed ? 'mdi-chevron-down' : 'mdi-chevron-up' }}</v-icon>
      </VBtn>
    </div>

    <div v-if="!isCollapsed" class="information-section">
      <VDataTable
        :headers="[
          { title: 'Field', key: 'field', width: '200px' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="information"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.actions="{ item }">
          <VBtn
            icon
            size="small"
            variant="text"
            color="primary"
            @click="openEditDialog(item)"
          >
            <v-icon>tabler-edit</v-icon>
          </VBtn>
        </template>
      </VDataTable>
    </div>

    <!-- Dynamic Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      entity-type="participant"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />
  </div>
</template>

<style scoped>
  .basic-information-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .information-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }
</style>