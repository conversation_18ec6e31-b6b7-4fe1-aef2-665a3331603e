<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useParticipants } from '@/composables/participants/useParticipants';
  import { useAppStore } from '@/stores/app/appStore';

  const { state: { participantAddressInfo } } = useParticipants();
  const appStore = useAppStore();

  const editDialog = ref(false);
  const selectedField = ref({
    field: '',
    value: '',
    id: '',
    typename: ''
  });

  const filteredAddress = computed(() => {
    return participantAddressInfo.value.filter(item =>
      ['street', 'postalCode', 'city', 'country', 'state'].includes(item.field)
    );
  });

  const openEditDialog = (item: any) => {
    if (item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return;
    }
    selectedField.value = {
      field: item.field,
      value: item.value,
      id: item.id,
      typename: item.typename
    };
    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const updateField = (newValue: any) => {
    // Update logic here
    closeEditDialog();
  };
</script>

<template>
  <div class="address-container">
    <div class="header-container">
      <h2>Address</h2>
    </div>

    <div class="address-section">
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name', width: '200px' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
        ]"
        :items="filteredAddress"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.actions="{ item }">
          <VBtn
            icon
            size="small"
            variant="text"
            color="primary"
            @click="openEditDialog(item)"
          >
            <VIcon
              v-if="item?.disabled"
              size="16"
              icon="tabler-alert-triangle"
              class="edit-icon"
              color="error"
            />
            <VIcon
              v-else
              size="16"
              icon="tabler-edit"
              class="edit-icon"
              color="primary"
            />
          </VBtn>
        </template>
      </VDataTable>
    </div>

    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />
  </div>
</template>

<style scoped>
  .address-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .address-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .edit-icon {
    margin-left: 4px;
  }
</style>