<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useParticipants } from '@/composables/participants/useParticipants';
  import { useAppStore } from '@/stores/app/appStore';
  import PensionCodeDialog from '@/components/participants/pensionInfo/PensionCodeDialog.vue';

  const { state: { participantPensionInfo } } = useParticipants();
  const appStore = useAppStore();

  const editDialog = ref(false);
  const pensionCodeDialog = ref(false);
  const selectedField = ref({
    field: '',
    value: '',
    id: '',
    typename: ''
  });

  const pensionInfo = computed(() => {
    if (!participantPensionInfo.value) return [];
    const info = participantPensionInfo.value;
    const pendingChanges = info.pendingChanges || [];

    return [
      {
        field: 'Code',
        value: info.code?.toString() || 'N/A',
        disabled: false,
        hasPendingChanges: pendingChanges.includes('code')
      },
      {
        field: 'Code Description',
        value: info.codeDescription || 'N/A',
        disabled: true,
        hasPendingChanges: pendingChanges.includes('codeDescription')
      },

    ];
  });

  const openEditDialog = (item: any) => {
    if (item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return;
    }

    selectedField.value = {
      field: item.field,
      value: item.value,
      id: item.id,
      typename: item.typename
    };

    if (item.field === 'Code') {
      pensionCodeDialog.value = true;
    } else {
      editDialog.value = true;
    }
  };

  const closeEditDialog = () => {
    editDialog.value = false;
    pensionCodeDialog.value = false;
  };

  const updateField = (newValue: any) => {
    closeEditDialog();
  };

  const handleCodeChanged = (codeData: any) => {
    closeEditDialog();
    // Additional handling for code changes if needed
  };
</script>

<template>
  <v-card class="pension-info-container">
    <div class="header-container">
      <h2 class="text-h4 font-weight-medium">Pension Information</h2>
    </div>

    <div class="pension-info-section">
      <VDataTable
        :headers="[
          { title: 'Field', key: 'field' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
        ]"
        :items="pensionInfo"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.actions="{ item }">
          <VBtn
            v-if="item.field !== 'Code Description'"
            icon
            size="small"
            variant="text"
            color="primary"
            @click="openEditDialog(item)"
          >
            <VIcon
              v-if="item?.disabled"
              size="16"
              icon="tabler-alert-triangle"
              class="edit-icon"
              color="error"
            />
            <VIcon
              v-else-if="item?.hasPendingChanges"
              size="16"
              icon="tabler-alert-circle"
              class="edit-icon"
              color="warning"
            />
            <VIcon
              v-else
              size="16"
              icon="tabler-edit"
              class="edit-icon"
              color="primary"
            />
          </VBtn>
        </template>
      </VDataTable>
    </div>

    <div v-if="!pensionInfo.length" class="text-caption text-disabled pa-2">
      No pension information available.
    </div>

    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />

    <PensionCodeDialog
      v-if="pensionCodeDialog"
      v-model="pensionCodeDialog"
      :previousCode="participantPensionInfo.value?.previousCode"
      :current-code="selectedField.value ? parseInt(selectedField.value) : null"
      :participant-id="selectedField.id"
      :pension-info-id="selectedField.id"
      @close="closeEditDialog"
      @code-changed="handleCodeChanged"
    />
  </v-card>
</template>

<style scoped>
  .pension-info-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .pension-info-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .edit-icon {
    margin-left: 4px;
  }
</style>

