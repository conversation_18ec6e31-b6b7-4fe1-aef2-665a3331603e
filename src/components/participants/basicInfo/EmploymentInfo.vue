<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useParticipants } from '@/composables/participants/useParticipants';
  import { useAppStore } from '@/stores/app/appStore';

  const { state: { participantEmploymentInfo, participantPensionInfo } } = useParticipants();
  const appStore = useAppStore();

  const editDialog = ref(false);
  const selectedField = ref({
    field: '',
    value: '',
    id: '',
    typename: ''
  });

  const employmentInfo = computed(() => {
    if (!participantEmploymentInfo.value) return [];
    const info = participantEmploymentInfo.value;
    return [
      { field: 'Employee ID', value: info.employeeId || 'N/A', disabled: false },
      { field: 'Department', value: info.department || 'N/A', disabled: false },
      { field: 'Position', value: info.position || 'N/A', disabled: false },
      { field: 'Registration Number', value: info.regNum?.toString() || 'N/A', disabled: false },
      { field: 'HAV Number', value: info.havNum?.toString() || 'N/A', disabled: false },
      { field: 'Start Date', value: info.startDate ? new Date(info.startDate).toLocaleDateString() : 'N/A', disabled: false },
      { field: 'Status', value: info.status || 'N/A', disabled: false }
    ];
  });

  const openEditDialog = (item: any) => {
    if (item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return;
    }
    selectedField.value = {
      field: item.field,
      value: item.value,
      id: item.id,
      typename: item.typename
    };
    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const updateField = (newValue: any) => {
    // Update logic here
    closeEditDialog();
  };
</script>

<template>
  <v-card class="employment-info-container">
    <div class="header-container">
      <h2 class="text-h4 font-weight-medium">Employment Information</h2>
    </div>

    <div class="employment-info-section">
      <VDataTable
        :headers="[
          { title: 'Field', key: 'field' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
        ]"
        :items="employmentInfo"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.actions="{ item }">
          <VBtn
            icon
            size="small"
            variant="text"
            color="primary"
            @click="openEditDialog(item)"
          >
            <VIcon
              v-if="item?.disabled"
              size="16"
              icon="tabler-alert-triangle"
              class="edit-icon"
              color="error"
            />
            <VIcon
              v-else
              size="16"
              icon="tabler-edit"
              class="edit-icon"
              color="primary"
            />
          </VBtn>
        </template>
      </VDataTable>
    </div>

    <div v-if="!employmentInfo.length" class="text-caption text-disabled pa-2">
      No employment information available.
    </div>

    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />
  </v-card>
</template>

<style scoped>
  .employment-info-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .employment-info-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .edit-icon {
    margin-left: 4px;
  }
</style> 