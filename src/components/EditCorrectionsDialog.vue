<script setup lang="ts">
  import { ref, computed, PropType } from 'vue';
  import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal';
  import { useAppStore } from '@/stores/app/appStore';
  import { ChangeType } from '@/gql/graphql';

  const props = defineProps({
    field: { type: String, required: true },
    fieldName: { type: String, required: true },
    currentValue: { type: String, required: true },
    year: { type: Number, required: true },
    modelValue: { type: Boolean, required: true },
    entityType: { type: String, required: true },
    entityId: { type: String, required: true },
    type: { type: String as PropType<ChangeType>, required: true },
    participantName: { type: String, required: false }
  });

  const { state: { loadingCreateChangeProposal }, actions: { createChangeProposal } } = useChangeProposal();

  const appStore = useAppStore();
  const emit = defineEmits(['update:modelValue', 'save', 'close', 'refresh']);

  const correctionValue = ref<number | null>(0);
  const newValue = computed(() => {
    const currentVal = parseFloat(props.currentValue) || 0;
    const correctionVal = typeof correctionValue.value === 'string' ? parseFloat(correctionValue.value) || 0 : correctionValue.value || 0;

    return (currentVal + correctionVal).toString();
  });
  const effectiveDate = ref(`12/31/${props.year - 1}`);

  const isValueUnchanged = computed(() => {
    return correctionValue.value === null || correctionValue.value === 0;
  });

  const formattedEntityType = computed(() => {
    return props.entityType.replace(/([A-Z])/g, ' $1').trim();
  });

  const saveChanges = async () => {
    await createChangeProposal({
      participantName: props.participantName,
      entityId: props.entityId,
      entityType: props.entityType,
      path: props.field,
      newValue: newValue.value,
      oldValue: props.currentValue,
      effectiveDate: effectiveDate.value,
      type: props.type
    });
    appStore.showSnack('Change proposal created successfully');
    emit('refresh'); // Emit refresh event to trigger data reload
    emit('close');
  };

  const closeDialog = () => {
    emit('close');
  };
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 500"
    :model-value="modelValue"
    @update:model-value="(value: any) => emit('update:modelValue', value)"
  >
    <VCard class="pa-4">
      <VCardTitle class="text-h5 pb-2">
        Edit {{ fieldName }} for {{ formattedEntityType }}
      </VCardTitle>

      <VCardText>
        <div class="text-subtitle-1 font-weight-bold mb-1">Old Value</div>
        <VTextField
          :value="currentValue"
          variant="solo-filled"
          hide-details="auto"
          density="comfortable"
          class="mb-4"
          readonly
        />

        <div class="text-subtitle-1 font-weight-bold mb-1">Correction</div>
        <VTextField
          v-model="correctionValue"
          type="number"
          variant="outlined"
          hide-details="auto"
          density="comfortable"
          class="mb-4"
          :rules="[(v: any) => v !== null && v !== undefined || `Correction is required`]"
        />

        <div class="text-subtitle-1 font-weight-bold mb-1">New Value</div>
        <VTextField
          :value="newValue"
          variant="solo-filled"
          hide-details="auto"
          density="comfortable"
          class="mb-1"
          readonly
        />

        <div v-if="isValueUnchanged" class="text-warning mb-4">
          Please enter a non-zero correction value
        </div>
      </VCardText>

      <VCardActions class="pb-4 px-4">
        <VSpacer />
        <VBtn
          color="secondary"
          @click="closeDialog"
          min-width="100"
        >
          Cancel
        </VBtn>
        <VBtn
          variant="elevated"
          color="primary"
          :loading="loadingCreateChangeProposal"
          @click="saveChanges"
          min-width="100"
          :disabled="isValueUnchanged || loadingCreateChangeProposal"
          class="ml-3"
        >
          Save
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
