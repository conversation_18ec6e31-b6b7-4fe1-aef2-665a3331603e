<script setup lang="ts">
import useUsers from '@/composables/users/useUsers'
import {userTableHeaders} from "@/components/Users/<USER>";
import {useUserStore} from "@/stores/users/userStore";
import {UserInput} from "@/types/user.types";
import { confirmUserDeleteAlert } from '@/composables/useSweetAlert'

const userStore = useUserStore()

const {state:{loadingDelete}, actions:{handleDeleteUser}} = useUsers()

const editUsers = (user: UserInput) => {
  userStore.showUserFormDialog()
  userStore.setUserFormDetails(user)
}

const showDialog = () => {
  userStore.resetUserFormDetails()
  userStore.showUserFormDialog()
}

const deleteUser = async (user: UserInput) => {
  try {
    const confirmDelete = await confirmUserDeleteAlert(true)
    if(confirmDelete.isConfirmed){
      userStore.setUserFormDetails(user)
      await handleDeleteUser()
    }
  } catch (error) {
    console.error('Error deleting user:', error)
  }
}

const { state: { userList } } = useUsers()

const sortBy = ref([{ key: 'email', order: 'asc' }])
</script>

<template>
  <UserDialog />
  <VCard>
    <template v-slot:title>
      <v-row class="px-2">
        <v-col cols="6" class="text-h4 font-weight-medium">
          Manage Users
        </v-col>
        <v-col cols="6" class="d-flex justify-end">
          <VBtn
              @click="showDialog"
              size="small"
              variant="outlined"
              prepend-icon="tabler-plus"
          >
            Add User
          </VBtn>
        </v-col>
      </v-row>
    </template>
    <v-divider />
  <v-row>
    <v-col>
      <VDataTable
          :headers="userTableHeaders"
          v-model:sort-by="sortBy"
          :items="userList"
          :items-per-page="5"
          :search-props="{ outlined: true }"
      >
        <template #item.actions="{ item }">
          <VBtn @click="editUsers(item)" icon size="small" class="mx-2" variant="text">
            <VIcon icon="tabler-edit" />
          </VBtn>
          <VBtn icon size="small" variant="text" @click="deleteUser(item)">
            <VIcon icon="tabler-trash" />
          </VBtn>
        </template>
      </VDataTable>
    </v-col>

  </v-row>
  </VCard>
</template>
