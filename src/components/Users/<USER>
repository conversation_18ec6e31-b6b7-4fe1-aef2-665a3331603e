import { computed } from 'vue'

export const userTableHeaders = computed(() => {
    return [
        {
            title: "Firstname",
            sortable: false,
            key: 'firstname',
        },
        {
            title: "Lastname",
            sortable: false,
            key: 'lastname',
        },
        {
            title: "Email",
            sortable: false,
            key: 'email',
        },
        {
            title: "Role",
            sortable: false,
            key: 'role.name',
        },
        {
            title: '',
            sortable: false,
            key: 'actions',
        },
    ]
})
