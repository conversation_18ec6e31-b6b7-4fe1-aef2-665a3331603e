<script setup lang="ts">
  import {useUserStore} from "@/stores/users/userStore";
  import {useAppStore} from "@/stores/app/appStore";
  import useUsers from "@/composables/users/useUsers";

  const userStore = useUserStore()
  const appStore = useAppStore()

  const {state:{loadingUpdate, loadingCreate, rolesList}, actions:{handleCreateUser, handleUpdateUser}}  = useUsers()

  const resetForm = () => {
    userStore.resetUserFormDetails()
    userStore.hideUserFormDialog()
  }

  const onFormSubmit = async () => {
    try {
      if (
        !userStore.userFormDetails.email ||
        !userStore.userFormDetails.firstname ||
        !userStore.userFormDetails.lastname ||
        !userStore.userFormDetails.roleId
      ) {
        appStore.showSnack('Please fill all fields')
        return
      }
      if (userStore.userFormDetails.id) {
        await handleUpdateUser()
        appStore.showSnack('User updated successfully')
        return
      } else {
        await handleCreateUser()
        appStore.showSnack('User added successfully')
      }
      resetForm()
    } catch (error) {
      console.error('Error creating user:', error)
    }
  }

  const dialogModelValueUpdate = () => {
    userStore.hideUserFormDialog()
    userStore.resetUserFormDetails()
  }
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 650"
    :model-value="userStore.userFormDialog"
    @update:model-value="dialogModelValueUpdate"
  >
    <DialogCloseBtn @click="userStore.hideUserFormDialog()" />

    <VCard class="pa-sm-9 pa-5">
      <VCardItem v-if="userStore.userFormDetails.id">
        <VCardTitle class="text-h5 text-center mb-3">
          Update User
        </VCardTitle>
        <p class="text-center">Update User details</p>
      </VCardItem>
      <VCardItem v-else>
        <VCardTitle class="text-h5 text-center mb-3">
          Add New User
        </VCardTitle>
        <p class="text-center">Add a new user with specific role</p>
      </VCardItem>

      <VCardText>
        <VForm @submit.prevent="onFormSubmit">
          <VRow>
            <VCol cols="6">
              <VTextField
                v-model="userStore.userFormDetails.firstname"
                label="First Name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="6">
                <VTextField
                  v-model="userStore.userFormDetails.lastname"
                  label="Last Name"
                  :rules="[requiredValidator]"
                />
              </VCol>

            <VCol cols="6" md="6">
              <VTextField
                :disabled="userStore.userFormDetails?.id"
                v-model="userStore.userFormDetails.email"
                label="Email"
                type="email"
                :rules="[requiredValidator, emailValidator]"
              />
            </VCol>

            <VCol
                cols="6"
                sm="6"
            >
              <V-select
                  v-model="userStore.userFormDetails.roleId"
                  :items="rolesList"
                  item-title="name"
                  item-value="id"
                  label="Role"
              />
            </VCol>


            <VCol cols="12" class="text-center">
              <VBtn
                :loading="loadingCreate || loadingUpdate"
                :disabled="loadingCreate || loadingUpdate"
                type="submit"
                class="me-3"
              >
                Submit
              </VBtn>

              <VBtn variant="tonal" color="secondary" @click="resetForm">
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
