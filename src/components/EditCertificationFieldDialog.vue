<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
  import { useAppStore } from '@/stores/app/appStore'
  import { ChangeType } from '@/gql/graphql'
  import { usePensionStore } from '@/stores/pension/pensionStore'

  const props = defineProps({
    field: { type: String, required: true },
    fieldName: { type: String, required: true },
    currentValue: { type: String || Number , required: true },
    year: { type: Number, required: true },
    modelValue: { type: Boolean, required: true },
    entityType: { type: String, required: true },
    entityId: { type: String, required: true },
    type: { type: ChangeType, required: true },
    participantName: { type: String, required: false }
  });

  const {state:{loadingCreateChangeProposal}, actions:{createChangeProposal}} = useChangeProposal()

  const appStore = useAppStore()
  const pensionStore = usePensionStore()
  const emit = defineEmits(['update:modelValue', 'save', 'close']);

  const newValue = ref(props.currentValue);
  const updateReason = ref('');
  const effectiveDate = ref(`12/31/${props.year}`);

  const isValueUnchanged = computed(() => {
    return newValue.value === props.currentValue;
  });

  const saveChanges = async () =>  {
    await createChangeProposal({participantName: props.participantName, entityId: props.entityId, entityType: props.entityType, path: props.field, newValue: newValue.value, oldValue: props.currentValue, effectiveDate: effectiveDate.value, type: props.type, isCertificationProposal: true});
    appStore.showSnack('Change proposal created successfully');
    const camelCaseEntityType = props.entityType.charAt(0).toLowerCase() + props.entityType.slice(1);
    pensionStore.updatePendingChanges(props.year, props.field, camelCaseEntityType)
    emit('close');
  };

  const closeDialog = () => {
    emit('close');
  };
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 500"
    :model-value="modelValue"
    @update:model-value="(value) => emit('update:modelValue', value)"
  >
    <VCard class="pa-4">
      <VCardTitle class="text-h5 pb-2">
        Request changes on {{ fieldName }}
      </VCardTitle>

      <VCardText>
        <div class="text-subtitle-1 font-weight-bold mb-1">Current Value</div>
        <VTextField
          :value="currentValue"
          variant="solo-filled"
          hide-details="auto"
          density="comfortable"
          class="mb-4"
          readonly
        />

        <div class="text-subtitle-1 font-weight-bold mb-1">New Value</div>
        <VTextField
          :placeholder="currentValue"
          v-model="newValue"
          :value="newValue"
          variant="outlined"
          hide-details="auto"
          density="comfortable"
          class="mb-1"
          :rules="[(v) => !!v || `${field} is required`]"
        />

        <div v-if="isValueUnchanged" class="text-warning mb-4">
          New value is the same as current value
        </div>

<!--        <div class="text-subtitle-1 font-weight-medium mt-4 mb-1">Reason for proposed update</div>-->
        <div class="d-flex align-center">
<!--          <VTextarea-->
<!--            v-model="updateReason"-->
<!--            variant="outlined"-->
<!--            placeholder="Why is this change needed?"-->
<!--            density="comfortable"-->
<!--            class="flex-grow-1"-->
<!--          />-->
          <div v-if="entityType==='changeProposal'" class="ml-3 d-flex align-center text-grey">
            <VIcon icon="tabler-info-circle" class="mr-1" size="small" />
            <span>Always January 1st of the year</span>
          </div>
        </div>
      </VCardText>

      <VCardActions class="pb-4 px-4">
        <VSpacer />
        <VBtn
          color="secondary"
          @click="closeDialog"
          min-width="100"
        >
          Cancel
        </VBtn>
        <VBtn
          variant="elevated"
          color="primary"
          :loading="loadingCreateChangeProposal"
          @click="saveChanges"
          min-width="100"
          :disabled="!newValue || isValueUnchanged || loadingCreateChangeProposal"
          class="ml-3"
        >
          Save
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>