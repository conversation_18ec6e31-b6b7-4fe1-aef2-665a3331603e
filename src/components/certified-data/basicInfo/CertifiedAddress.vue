<script setup lang="ts">
  import { computed, ref, toRaw } from 'vue'
  import { useAppStore } from '@/stores/app/appStore'
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import { ChangeType } from '@/gql/graphql'
  import RevertApproveRejectCertFields from '@/components/certified-data/RevertApproveRejectCertFields.vue'

  interface FieldItem {
    __typename?: string;
    typename?: string;
    id: string;
    name: string;
    field: string;
    value: any;
    disabled: boolean;
    isDifferent?: boolean;
    changeRequested?: boolean;
    approvedChanges?: boolean;
    rejectReason?: any
  }

  const props = defineProps({
    editable: {
      type: Boolean,
      default: true
    },
    activeCertification: {
      type: Boolean,
      default: false
    },
    year: {
      type: Number,
      default: 2024
    }
  });

  const pensionStore = usePensionStore()
  const appStore = useAppStore()
  
  // Get address info from certifiedData
  const addressInfoToBeCertified = computed(() => {
    try {
      if (!pensionStore || !pensionStore.certifiedDataByYearAndYearBefore) {
        console.warn('Pension store or certified data not available');
        return null;
      }
      
      const yearData = pensionStore.certifiedDataByYearAndYearBefore[props.year];
      if (!yearData || !Array.isArray(yearData) || yearData.length === 0) {
        console.warn(`No certified data found for year ${props.year}`);
        return null;
      }
      
      const certifiedData = yearData[0];
      return certifiedData?.certifiedAddress || null;
    } catch (error) {
      console.error('Error getting address info:', error);
      return null;
    }
  });

  const yearInReview = computed(() => props.year);
  const normalizedData = computed(() => pensionStore.normalizedCertifiedData);
  const certifiedDataId = computed(() => normalizedData.value[yearInReview.value]?.id);
  const onGoingCertification = computed(() => pensionStore.onGoingCertification);
  const activeCertification = computed(() => onGoingCertification.value && !!props.editable);

  // Collect all reject reasons
  const allRejectReasons = computed(() => {
    const reasons: Array<{ field: string, fieldKey: string, reason: string, disabled: boolean }> = [];
    const addressInfo = addressInfoToBeCertified.value;

    if (addressInfo?.certificationRejectReason && Array.isArray(addressInfo.certificationRejectReason)) {
      addressInfo.certificationRejectReason.forEach((item: any) => {
        if (item.reason && item.status === 'VALID') {
          const fieldData = processedAddressData.value.find(f => f.field === item.field);
          reasons.push({
            field: fieldKeyToName(item.field),
            fieldKey: item.field,
            reason: item.reason,
            disabled: fieldData?.disabled || false
          });
        }
      });
    }

    return reasons;
  });

  function fieldKeyToName(fieldKey: string): string {
    if (!fieldKey) return '';
    const result = fieldKey.replace(/([A-Z])/g, ' $1');
    return result.charAt(0).toUpperCase() + result.slice(1).trim();
  }

  const processedAddressData = computed((): FieldItem[] => {
    const addressInfo = addressInfoToBeCertified.value;
    if (!addressInfo) return [];

    const transformed: FieldItem[] = [];
    const relevantFields = ['street', 'houseNumber', 'houseNumberAddition', 'postalCode', 'city', 'country'];
    const pendingChanges = addressInfo.pendingChanges || [];
    const differences = addressInfo.differences || [];
    const requestedChanges = addressInfo.requestedChanges || [];
    const approvedChanges = addressInfo.approvedChanges || [];

    relevantFields.forEach(key => {
      if (Object.prototype.hasOwnProperty.call(addressInfo, key) && addressInfo[key] !== undefined && addressInfo[key] !== null) {
        transformed.push({
          typename: 'CertifiedAddress',
          id: addressInfo.id,
          name: fieldKeyToName(key),
          field: key,
          value: addressInfo[key],
          disabled: pendingChanges.includes(key),
          isDifferent: differences.includes(key),
          changeRequested: requestedChanges.includes(key),
          approvedChanges: approvedChanges.includes(key),
          rejectReason: addressInfo.certificationRejectReason?.find((item: any) => item.field === key)?.reason
        });
      }
    });

    return transformed;
  });

  const displayableFields = computed(() => {
    return processedAddressData.value;
  });

  const emit = defineEmits(['update:address']);

  const editDialog = ref(false);
  const selectedField = ref<FieldItem | null>(null);

  const openEditDialog = (item: FieldItem) => {
    if (item.changeRequested) {
      return openDialog(item);
    }

    if (!isFieldEditable(item)) {
      return false;
    }

    if(item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return false;
    }

    openDialog(item);
  };

  const openDialog = (item: FieldItem) => {
    selectedField.value = item;
    editDialog.value = true;
  };

  const isFieldEditable = (item: FieldItem): boolean => {
    if (!props.editable) {
      return false;
    }

    if (item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return false;
    }

    return true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
    selectedField.value = null;
  };

  const updateField = (updatedValue: string) => {
    console.log("Field updated (placeholder):", selectedField.value?.field, updatedValue);
    closeEditDialog();
  };

  const showApprovalDialog = ref(false);
  const fieldToApprove = ref<FieldItem | null>(null);

  const showRevertDialog = ref(false);
  const fieldToRevert = ref<FieldItem | null>(null);

  // Determine chip color based on field state
  const getChipColor = (field: FieldItem): string => {
    if (field.approvedChanges) return 'success';
    if (field.changeRequested) return 'primary';
    if (field.isDifferent) return 'error';
    return 'default';
  };

  // Determine chip icon based on field state
  const getChipIcon = (field: FieldItem): string => {
    if (field.approvedChanges && field.isDifferent) return 'tabler-check';
    if (field.changeRequested) return 'tabler-clock';
    if (field.isDifferent) return 'tabler-alert-triangle';
    return '';
  };

  // Get tooltip text for chips
  const getChipTooltip = (field: FieldItem): string => {
    if (field.approvedChanges) return 'Changes approved';
    if (field.changeRequested) return 'Changes requested';
    if (field.isDifferent) return 'There is a difference in values';
    return '';
  };

  // Determine if action button should be shown
  const shouldShowActionButton = (field: FieldItem): boolean => {
    return Boolean(field.isDifferent || field.changeRequested);
  };

  // Determine if action button should be disabled
  const isActionButtonDisabled = (field: FieldItem): boolean => {
    return Boolean(field.changeRequested || field.approvedChanges);
  };

  // Get action button color
  const getActionButtonColor = (field: FieldItem): string => {
    if (field.changeRequested || field.approvedChanges) return 'grey';
    if (field.isDifferent) return 'warning';
    return 'primary';
  };

  // Get action button icon
  const getActionButtonIcon = (field: FieldItem): string => {
    if (field.changeRequested) return 'tabler-clock';
    if (field.approvedChanges) return 'tabler-check';
    if (field.isDifferent) return 'tabler-gavel';
    return 'tabler-dots-vertical';
  };

  // Get action button tooltip
  const getActionButtonTooltip = (field: FieldItem): string => {
    if (field.changeRequested) return 'Change request pending';
    if (field.approvedChanges) return 'Changes already approved';
    if (field.isDifferent) return 'Review and approve/reject changes';
    return 'Actions';
  };

  // Handle action button click
  const handleActionClick = (field: FieldItem) => {
    console.log('Action button clicked for field:', toRaw(field));
    console.log('Field details:', {
      field: field.field,
      isDifferent: field.isDifferent,
      changeRequested: field.changeRequested,
      approvedChanges: field.approvedChanges
    });
    
    if (isActionButtonDisabled(field)) {
      console.log('Action button is disabled, returning');
      return;
    }

    if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
      console.log('Opening approval dialog for field:', field.field);
      fieldToApprove.value = field;
      showApprovalDialog.value = true;
    }
  };

  // Get action button menu items
  const getActionMenuItems = (field: FieldItem) => {
    const items = [];

    if (field.isDifferent && !field.changeRequested && !field.approvedChanges) {
      items.push({
        title: 'Approve/Reject',
        icon: 'tabler-gavel',
        action: () => {
          fieldToApprove.value = field;
          showApprovalDialog.value = true;
        }
      });
    }

    if (field.changeRequested || field.approvedChanges) {
      items.push({
        title: 'Revert Changes',
        icon: 'tabler-rotate-clockwise',
        action: () => {
          fieldToRevert.value = field;
          showRevertDialog.value = true;
        }
      });
    }

    return items;
  };

  const getFormattedAddress = computed(() => {
    const addressInfo = addressInfoToBeCertified.value;
    if (!addressInfo) return 'No address available';

    const parts = [
      addressInfo.street,
      addressInfo.houseNumber,
      addressInfo.houseNumberAddition,
      addressInfo.postalCode,
      addressInfo.city,
      addressInfo.country
    ].filter(Boolean);

    return parts.join(' ');
  });

</script>

<template>
  <div class="address-container">
    <div class="header-container">
      <h2>Address</h2>
      <div v-if="addressInfoToBeCertified" class="text-caption text-disabled">
        {{ getFormattedAddress }}
      </div>
    </div>

    <!-- Individual alerts for each rejected field -->
    <div v-if="allRejectReasons.length > 0 && !activeCertification" class="mb-4">
      <VAlert
        v-for="(item, index) in allRejectReasons"
        :key="`reject-${item.fieldKey}-${index}`"
        :type="item.disabled ? 'info' : 'error'"
        :variant="item.disabled ? 'tonal' : 'tonal'"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            :icon="item.disabled ? 'tabler-check' : 'tabler-x'"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.field }}:</strong>
          <span>{{ item.reason }}</span>
          <VChip
            v-if="item.disabled"
            size="small"
            color="primary"
            class="ml-auto"
          >
            Follow-up submitted
          </VChip>
        </div>
      </VAlert>
    </div>

    <div v-if="activeCertification">
      <!-- Active certification view with chips and action buttons -->
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="displayableFields"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.value="{ item }">
          <v-chip
            v-if="item.isDifferent || item.changeRequested"
            :model-value="true"
            class="ma-2"
            size="small"
            :color="getChipColor(item)"
            :prepend-icon="getChipIcon(item)"
          >
            {{ item.value }}
            <v-tooltip activator="parent" location="top">
              {{ getChipTooltip(item) }}
            </v-tooltip>
          </v-chip>
          <span v-else>{{ item.value }}</span>
        </template>
        <template v-slot:item.actions="{ item }">
          <v-menu v-if="shouldShowActionButton(item)">
            <template v-slot:activator="{ props }">
              <v-btn
                icon
                size="small"
                variant="text"
                :color="getActionButtonColor(item)"
                v-bind="props"
                :disabled="isActionButtonDisabled(item)"
              >
                <v-icon :icon="getActionButtonIcon(item)" />
              </v-btn>
            </template>

            <v-list>
              <v-list-item
                v-for="menuItem in getActionMenuItems(item)"
                :key="menuItem.title"
                @click="menuItem.action"
              >
                <template v-slot:prepend>
                  <v-icon :icon="menuItem.icon" />
                </template>
                <v-list-item-title>{{ menuItem.title }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </template>
      </VDataTable>
    </div>
    <div v-else>
      <!-- Regular view with edit capability -->
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="displayableFields"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <template #item.value="{ item }">
          <template v-if="item.changeRequested">
            <v-badge
              v-if="!item.disabled && item.changeRequested"
              color="error"
              size="small"
              icon="tabler-exclamation-mark"
            >
              <VChip
                class="font-weight-bold"
                label
                :color="item.disabled ? 'primary' : 'error'"
                density="compact"
              >
                {{ item.value }}
              </VChip>
            </v-badge>
            <VChip
              v-else
              class="font-weight-bold"
              label
              :color="item.disabled ? 'primary' : 'error'"
              density="compact"
            >
              {{ item.value }}
            </VChip>
          </template>
          <template v-else>
            {{ item.value }}
          </template>
        </template>
        <template #item.actions="{ item }">
          <VBtn
            v-if="props.editable || item.changeRequested"
            icon
            size="small"
            variant="text"
            color="primary"
            @click="openEditDialog(item)"
          >
            <VIcon
              v-if="item?.disabled"
              size="16"
              icon="tabler-alert-triangle"
              class="edit-icon ms-1 ml-4"
              color="error"
            />
            <VIcon
              v-else
              size="16"
              icon="tabler-edit"
              class="edit-icon ms-1 ml-4"
              color="primary"
            />
          </VBtn>
        </template>
      </VDataTable>
    </div>

    <div v-if="!addressInfoToBeCertified" class="text-caption text-disabled pa-2">
      No address information available.
    </div>

    <!-- Dynamic Dialog -->
    <EditCertificationFieldDialog
      v-if="editDialog && selectedField"
      v-model="editDialog"
      :field="selectedField.field"
      :field-name="selectedField.name"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      entity-type="CertifiedAddress"
      :participant-name="getFormattedAddress"
      :year="yearInReview"
      @close="closeEditDialog"
      @update="updateField"
      :type="ChangeType.CertifiedData"
    />

    <ApproveRejectCertFields
      v-model="showApprovalDialog"
      :field="fieldToApprove"
      :year="yearInReview"
      entityType="certifiedAddress"
      :participant-name="getFormattedAddress"
      :certifiedDataId="certifiedDataId"
      @close="showApprovalDialog = false"
    />

    <RevertApproveRejectCertFields
      v-model="showRevertDialog"
      :field="fieldToRevert"
      :year="yearInReview"
      entityType="certifiedAddress"
      :participant-name="getFormattedAddress"
      :certifiedDataId="certifiedDataId"
      @close="showRevertDialog = false"
    />
  </div>
</template>

<style scoped>
  .address-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .address-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }
</style>