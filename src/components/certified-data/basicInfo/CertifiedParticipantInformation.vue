<script setup lang="ts">
  import Address from '@/components/participants/basicInfo/Address.vue'

  const props = defineProps({
    editable: {
      type: Boolean,
      default: true
    },
    year: {
      type: Number,
      default: 2024
    },
    activeCertification: {
      type: Boolean,
      default: false
    }
  });

  const propertyListingSteps = [
    {
      title: 'Marital Status & Partner',
      subtitle: 'Marital and partner details',
      icon: 'tabler-heart',
    },
    {
      title: 'Ex Partners',
      subtitle: 'Ex Partner details',
      icon: 'tabler-users-plus',
    },
    {
      title: 'Children',
      subtitle: 'Children details',
      icon: 'tabler-accessible',
    },
    {
      title: 'Address',
      subtitle: 'Address details',
      icon: 'tabler-address-book',
    },
  ];

  const currentStep = ref(0);
</script>

<template>
  <VCard>
    <VRow no-gutters>
      <VCardText>
        <AppStepper
          v-model:current-step="currentStep"
          :items="propertyListingSteps"
          icon-size="22"
          class="stepper-icon-step-bg"
        />
      </VCardText>

      <VCol cols="12">
        <VCardText>
          <VWindow v-model="currentStep" class="disable-tab-transition">

            <VWindowItem>
              <CertifiedMaritalStatus :editable="props.editable" :year="props.year" :activeCertification="props.activeCertification" />
            </VWindowItem>
            <VWindowItem>
              <CertifiedExPartners :editable="props.editable" :year="props.year" :activeCertification="props.activeCertification" />
            </VWindowItem>
            <VWindowItem>
              <CertifiedChildren :editable="props.editable" :year="props.year" :activeCertification="props.activeCertification" />
            </VWindowItem><VWindowItem>
              <CertifiedAddress :editable="props.editable" :year="props.year" :activeCertification="props.activeCertification" />
            </VWindowItem>
          </VWindow>
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>

<style scoped>
  .stepper-icon-step-bg {
    margin-bottom: 16px;
  }
</style>