<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useRoute } from 'vue-router'
  import { usePensionBase } from '@/composables/pension-base/usePensionBase'
  import { useCertifiedData } from '@/composables/certified-data'
  import { useAppStore } from '@/stores/app/appStore'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const route = useRoute()
  const appStore = useAppStore()
  const { getCertificationStatus } = usePensionBase()
  const { actions: { updateCertificationStatus }, state: { loadingUpdateCertificationStatus } } = useCertifiedData()

  const certificationYear = computed(() => {
    return route.params.year ? parseInt(route.params.year as string) : new Date().getFullYear() - 1
  })

  const certificationStatus = computed(() => {
    return getCertificationStatus(certificationYear.value)
  })

  const isEditable = computed(() => {
    return route.path.includes('/started') && certificationStatus.value === 'started'
  })

  const confirmDialog = ref(false)
  const isSwitchingToManaged = ref<boolean | null>(null)

  const handleShowDialog = () => {
    confirmDialog.value = true
  }

  const onSetCertificationStatus = async (status: string) => {
    try {
      // Get the certification ID from the route params
      const certificationId = route.params.cid as string

      if (!certificationId) {
        appStore.showSnack('Certification ID not found')
        return
      }

      // Call the mutation to update the certification status
      await updateCertificationStatus(certificationId, status)

      // Show success message
      appStore.showSnack(`Certification status updated to ${status}`)

      // Navigate to the updated certification page
      router.push(`/certification-management/${route.params.id}/certification/${route.params.cid}/${certificationYear.value}/${status}`)

      // Close the dialog
      confirmDialog.value = false
    } catch (error) {
      console.error('Error updating certification status:', error)
      appStore.showSnack('Failed to update certification status')
    }
  }

  const goToCertification = () => {
    router.push(`/certification-management/${route.params.id}/certification/${route.params.cid}/${certificationYear.value}/${certificationStatus.value}`)
  }

  const onCloseDialog = () => {
    confirmDialog.value = false
    isSwitchingToManaged.value = null
  }

  const nextStatus = computed(() => {
    return certificationStatus.value === 'pending' ? 'started' : 'completed'
  })

  // Get alert properties based on certification status
  const alertProps = computed(() => {
    switch (certificationStatus.value) {
      case 'completed':
        return {
          title: `Certification for ${certificationYear.value} has been completed.`,
          type: 'success',
          message: 'The certification process for this year has been completed.',
          showAction: false
        }
      case 'started':
        return {
          title: `Certification for ${certificationYear.value} is in progress.`,
          type: 'warning',
          message: 'Would you like to complete the certification process?',
          showAction: isEditable.value,
          actionText: 'Complete Now',
          actionStatus: 'completed'
        }
      case 'pending':
      default:
        return {
          title: `Data for ${certificationYear.value} is not ready for Certification.`,
          type: 'info',
          message: 'Would you like to set as ready certification?',
          showAction: true,
          actionText: 'Ready for certification',
          actionStatus: 'started'
        }
    }
  })
</script>

<template>
  <div>
    <v-row>
      <v-col class="mb-4" cols="12" align="end">
        <v-btn size="small" variant="outlined" color="primary" @click="goToCertification">Certification Page</v-btn>
      </v-col>
    </v-row>
    <v-alert
      :title="alertProps.title"
      :type="alertProps.type"
      variant="tonal"
    >
      <v-row align="center" no-gutters>
        <v-col cols="8" sm="9" md="10">
          <div class="d-flex align-center">
            <v-chip
              size="small"
              :color="alertProps.type"
              class="status-chip mr-2"
            >
              {{ certificationStatus }}
            </v-chip>
            <div class="text-caption">{{ alertProps.message }}</div>
          </div>
        </v-col>

        <v-col v-if="alertProps.showAction" cols="4" sm="3" md="2" class="text-right">
          <v-btn
            size="small"
            :color="alertProps.type === 'info' ? 'success' : 'warning'"
            @click="handleShowDialog()"
            variant="flat"
            class="ml-2"
          >
            {{ alertProps.actionText }}
            <v-icon end icon="mdi-arrow-right"></v-icon>
          </v-btn>
        </v-col>
      </v-row>
    </v-alert>

    <v-dialog v-model="confirmDialog" persistent width="auto">
      <v-card>
        <v-card-title>
          <span class="text-h5">Confirmation</span>
        </v-card-title>
        <v-card-text>
          Are you sure you want to {{ nextStatus === 'started' ? 'start' : 'complete' }} certification for {{ certificationYear }}?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey-darken-1" variant="text" @click="onCloseDialog">
            Cancel
          </v-btn>
          <v-btn
            :color="nextStatus === 'started' ? 'primary' : 'success'"
            @click="onSetCertificationStatus(nextStatus)"
            :loading="loadingUpdateCertificationStatus"
            :disabled="loadingUpdateCertificationStatus"
          >
            {{ nextStatus === 'started' ? 'Start' : 'Complete' }} Certification
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped lang="scss">
  .v-alert {
    margin-bottom: 16px;
  }

  .status-chip {
    text-transform: capitalize;
  }
</style>