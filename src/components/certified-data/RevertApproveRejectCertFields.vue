<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAppStore } from '@/stores/app/appStore';
import { useCertifiedDataByYearAndYearBefore } from '@/composables/certified-data/useCertifiedDataByYearAndYearBefore'

interface FieldItem {
  __typename?: string;
  typename?: string;
  id: string;
  name: string;
  field: string;
  value: any;
  disabled: boolean;
  isDifferent?: boolean;
  changeRequested?: boolean;
  approvedChanges?: boolean;
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  field: {
    type: Object as () => FieldItem | null,
    required: true
  },
  year: {
    type: Number,
    required: true
  },
  participantName: {
    type: String,
    required: true
  },
  entityType: {
    type: String,
    required: true
  },
  certifiedDataId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:modelValue', 'close', 'reverted']);

const appStore = useAppStore();

const { actions: { handleRevertSingleField } } = useCertifiedDataByYearAndYearBefore()

const dialog = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

const closeDialog = () => {
  dialog.value = false;
  emit('close');
};

const revertChanges = async () => {
  try {
    if (props.field) {
      const result = await handleRevertSingleField({
        entityId: props.field.id,
        entityType: props.entityType,
        path: props.field.field
      });
      appStore.showSnack('Field changes reverted successfully');
      emit('reverted', { field: props.field.field, result });
    }
  } catch (error) {
    console.error('Error reverting field changes:', error);
    appStore.showSnack('Error reverting field changes');
  } finally {
    closeDialog();
  }
};
</script>

<template>
  <v-dialog v-model="dialog" max-width="500">
    <v-card v-if="field">
      <v-card-title class="text-h5">
        Revert Changes
      </v-card-title>
      <v-card-text>
        <div class="mb-4">
          Are you sure you want to revert all changes for <strong>{{ field.name }}</strong>?
        </div>
        <div class="d-flex flex-column gap-2">
          <div>
            <span class="text-subtitle-2 font-weight-medium">Field:</span>
            <span class="ml-2">{{ field.name }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Value:</span>
            <span class="ml-2">{{ field.value }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Participant:</span>
            <span class="ml-2">{{ participantName }}</span>
          </div>
          <div>
            <span class="text-subtitle-2 font-weight-medium">Year:</span>
            <span class="ml-2">{{ year }}</span>
          </div>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="secondary" @click="closeDialog">
          Cancel
        </v-btn>
        <v-btn color="error" @click="revertChanges">
          Revert
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template> 