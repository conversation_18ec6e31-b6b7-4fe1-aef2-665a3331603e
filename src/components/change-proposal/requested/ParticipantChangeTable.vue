<script setup lang="ts">
  import { ref, computed } from 'vue';
  import type { ParticipantChange } from '@/types/participant.types';
  import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal';
  import { participantChangeHeaders } from '@/components/change-proposal/ChangeProposalTableHeaders'
  import { confirmChangeApproval } from '@/composables/useSweetAlert'

  const {
    state: { participantChangeProposalsList, isLoading: loadingAction, loadingParticipantsChangeProposals:loadingTable },
    actions: { handleApproveChangeProposal, handleRejectChangeProposal }
  } = useChangeProposal();

  const searchInput = ref('');
  const isLoading = ref(false);
  const appliedFilters = ref({});
  const itemsPerPage = ref(10);
  const page = ref(1);


  const showRejectDialog = ref(false);
  const currentProposalId = ref('');

  const openRejectDialog = (proposalId: string) => {
    currentProposalId.value = proposalId;
    showRejectDialog.value = true;
  };

  const handleDialogClose = () => {
    showRejectDialog.value = false;
  };


  const filteredChangeProposals = computed(() => {
    let filtered = [...participantChangeProposalsList.value];

    if (searchInput.value) {
      const searchLower = searchInput.value.toLowerCase();
      filtered = filtered.filter(item =>
        item.participant.toLowerCase().includes(searchLower) ||
        item.field.toLowerCase().includes(searchLower) ||
        item.proposedBy.toLowerCase().includes(searchLower)
      );
    }

    // Apply advanced filters
    if (appliedFilters.value) {
      const filters = appliedFilters.value as any;

      if (filters.participant) {
        filtered = filtered.filter(item => item.participant === filters.participant);
      }

      if (filters.field) {
        filtered = filtered.filter(item => item.field === filters.field);
      }

      if (filters.status) {
        filtered = filtered.filter(item => item.status.toLowerCase() === filters.status.toLowerCase());
      }

      if (filters.proposedBy) {
        filtered = filtered.filter(item => item.proposedBy === filters.proposedBy);
      }

      if (filters.effectiveDateRange && filters.effectiveDateRange.startDate && filters.effectiveDateRange.endDate) {
        const startDate = new Date(filters.effectiveDateRange.startDate);
        const endDate = new Date(filters.effectiveDateRange.endDate);

        filtered = filtered.filter(item => {
          const effectiveDate = new Date(item.effectiveDate);
          return effectiveDate >= startDate && effectiveDate <= endDate;
        });
      }
    }

    return filtered;
  });

  // Pagination
  const paginatedData = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return filteredChangeProposals.value.slice(start, end);
  });

  const totalItems = computed(() => filteredChangeProposals.value.length);

  const paginationMeta = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value + 1;
    const end = Math.min(start + itemsPerPage.value - 1, totalItems.value);
    return `Showing ${start} to ${end} of ${totalItems.value} entries`;
  });

  const updateFilters = (newFilters: any) => {
    isLoading.value = true;
    appliedFilters.value = newFilters;
    page.value = 1;

    setTimeout(() => {
      isLoading.value = false;
    }, 200);
  };

  const handleApprove = async (change: ParticipantChange) => {
    const confirmApprove = await confirmChangeApproval(true)
    if (confirmApprove.isConfirmed) {
      await handleApproveChangeProposal(change.id);
    }
  };

    const handleRejectProposal = async (data: { proposalId: string, reason: string }) => {
      console.log(data);
    await handleRejectChangeProposal(data.proposalId, data.reason);
  };

  const resetAllFilters = () => {
    searchInput.value = '';
    appliedFilters.value = {};
  };
</script>

<template>
  <RejectChangeDialog
    :is-open="showRejectDialog"
    :proposal-id="currentProposalId"
    @close="handleDialogClose"
    @reject="handleRejectProposal"
  />
  <v-skeleton-loader
    v-if="loadingTable"
    class="mx-auto border py-6"
    type="table"
  ></v-skeleton-loader>
  <v-card v-else variant="outlined" title="Participant Change Proposals" class="mb-4 pa-2">
    <v-expand-transition>
      <div>
        <v-card-text class="pt-0 pb-0">
          <ChangeFilterTest
            :changeProposalsList="participantChangeProposalsList"
            @update:filters="updateFilters"
          />

          <!-- Search input -->
          <div class="d-flex justify-end align-center mb-4">
            <v-text-field
              v-model="searchInput"
              placeholder="Search by participant, field, or proposer..."
              density="compact"
              hide-details
              variant="outlined"
              bg-color="white"
              class="max-width-400"
              prepend-inner-icon="mdi-magnify"
              clearable
              @click:clear="searchInput = ''"
            ></v-text-field>
          </div>
        </v-card-text>

        <v-data-table
          :headers="participantChangeHeaders"
          :items="paginatedData"
          :items-per-page="-1"
          :loading="isLoading"
          class="elevation-0"
        >
          <!-- Current value column formatting -->
          <template v-slot:item.currentValue="{ item }">
            <span v-if="item.currentValue === '[object Object]'">[object Object]</span>
            <span v-else-if="item.currentValue === '-'">-</span>
            <span v-else-if="typeof item.currentValue === 'string' && item.currentValue.endsWith('%')">
              {{ item.currentValue }}
            </span>
            <span v-else>{{ item.currentValue }}</span>
          </template>

          <!-- New value column formatting with color highlight -->
          <template v-slot:item.newValue="{ item }">
            <span v-if="item.field === 'email'" class="blue--text">{{ item.newValue }}</span>
            <span v-else-if="item.field === 'date of birth'" class="blue--text">{{ item.newValue }}</span>
            <span v-else-if="typeof item.newValue === 'string' && item.newValue.includes('%')" class="blue--text">
              {{ item.newValue }}
            </span>
            <span v-else>{{ item.newValue }}</span>
          </template>

          <!-- Actions column -->
          <template v-slot:item.actions="{ item }">
            <div class="d-flex justify-end">
              <v-btn
                color="primary"
                size="small"
                variant="text"
                icon
                @click="handleApprove(item)"
              >
                <v-tooltip
                  activator="parent"
                  location="top"
                >Approve</v-tooltip>
                <v-icon>tabler-check</v-icon>
              </v-btn>
              <v-btn
                color="error"
                size="small"
                variant="text"
                icon
                @click="openRejectDialog(item?.id)"
              >
                <v-tooltip
                  activator="parent"
                  location="top"
                >Reject</v-tooltip>
                <v-icon>tabler-cancel</v-icon>
              </v-btn>
            </div>
          </template>

          <!-- Empty state -->
          <template v-slot:no-data>
            <div class="d-flex flex-column justify-center align-center pa-4">
              <span class="text-subtitle-1 mb-2">No pending changes found</span>
              <v-btn
                v-if="searchInput || Object.keys(appliedFilters).length > 0"
                variant="outlined"
                size="small"
                color="primary"
                @click="resetAllFilters"
              >
                Clear Filters
              </v-btn>
            </div>
          </template>

          <!-- Pagination -->
          <template #bottom>
            <v-divider />
            <div
              class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
            >
              <p class="text-sm text-disabled mb-0">
                {{ paginationMeta }}
              </p>

              <v-pagination
                v-model="page"
                :length="Math.ceil(totalItems / itemsPerPage)"
                :total-visible="$vuetify ? ($vuetify.display.xs ? 1 : $vuetify.display.md ? 5 : 7) : 7"
              >
                <template #prev="slotProps">
                  <v-btn
                    variant="tonal"
                    color="default"
                    v-bind="slotProps"
                    :icon="false"
                  >
                    Previous
                  </v-btn>
                </template>

                <template #next="slotProps">
                  <v-btn
                    variant="tonal"
                    color="default"
                    v-bind="slotProps"
                    :icon="false"
                  >
                    Next
                  </v-btn>
                </template>
              </v-pagination>

              <v-select
                v-model="itemsPerPage"
                :items="[5, 10, 25, 50]"
                label="Per page"
                density="compact"
                variant="outlined"
                hide-details
                class="items-per-page-select"
                style="width: 100px;"
              ></v-select>
            </div>
          </template>
        </v-data-table>
      </div>
    </v-expand-transition>
  </v-card>
</template>

<style scoped>
  .max-width-400 {
    max-width: 400px;
  }

  .blue--text {
    color: #1976d2 !important;
  }

  .items-per-page-select {
    min-width: 80px;
  }
</style>