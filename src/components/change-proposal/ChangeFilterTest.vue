<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { formatDateToString } from '@core/utils/formatters'

  const props = defineProps({
    changeProposalsList: {
      type: Array,
      required: true
    }
  })

  const emit = defineEmits(['update:filters'])

  const isFilterPanelOpen = ref(false)
  const loadingFilteredChanges = ref(false)

  const filterOptions = computed(() => {
    const participants = new Set()
    const fields = new Set()
    const proposedBy = new Set()
    const statuses = new Set()

    props.changeProposalsList.forEach(item => {
      participants.add(item.participant)
      fields.add(item.field)
      proposedBy.add(item.proposedBy)
      statuses.add(item.status)
    })

    return {
      participants: Array.from(participants).map(item => ({ id: item, text: item })),
      fields: Array.from(fields).map(item => ({ id: item, text: item })),
      proposedBy: Array.from(proposedBy).map(item => ({ id: item, text: item })),
      statuses: Array.from(statuses).map((item: any) => ({
        id: item,
        text: item.charAt(0).toUpperCase() + item.slice(1)
      }))
    }
  })

  // Filter state
  const filters = ref({
    participant: null,
    field: null,
    status: null,
    proposedBy: null,
    effectiveDateRange: {
      startDate: null,
      endDate: null
    }
  })

  // Date range menu state
  const dateRangeMenu = ref(false)

  const dateRangeDisplay = computed(() => {
    const { startDate, endDate } = filters.value.effectiveDateRange
    if (startDate && endDate) {
      return `${formatDateToString(startDate)} - ${formatDateToString(endDate)}`
    }
    return 'Select Date Range'
  })

  // Method to clear all filters
  const resetFilters = () => {
    filters.value = {
      participant: null,
      field: null,
      status: null,
      proposedBy: null,
      effectiveDateRange: {
        startDate: null,
        endDate: null
      }
    }
    dateRangeMenu.value = false
    emit('update:filters', {})
  }

  // Clear date range filter
  const clearDateRangeFilter = () => {
    filters.value.effectiveDateRange.startDate = null
    filters.value.effectiveDateRange.endDate = null
    dateRangeMenu.value = false
  }

  // Toggle filter panel
  const toggleFilterPanel = () => {
    isFilterPanelOpen.value = !isFilterPanelOpen.value
  }

  // Apply filters
  const applyFilters = () => {
    loadingFilteredChanges.value = true

    // Prepare filters for emitting
    const preparedFilters = {
      participant: filters.value.participant?.id || null,
      field: filters.value.field?.id || null,
      status: filters.value.status?.id || null,
      proposedBy: filters.value.proposedBy?.id || null,
      effectiveDateRange: {
        startDate: filters.value.effectiveDateRange.startDate,
        endDate: filters.value.effectiveDateRange.endDate
      }
    }

    emit('update:filters', preparedFilters)

    // Simulate loading
    setTimeout(() => {
      loadingFilteredChanges.value = false
    }, 500)
  }

  // Count applied filters
  const appliedFiltersCount = computed(() => {
    let count = 0
    if (filters.value.participant) count++
    if (filters.value.field) count++
    if (filters.value.status) count++
    if (filters.value.proposedBy) count++
    if (filters.value.effectiveDateRange.startDate && filters.value.effectiveDateRange.endDate) count++
    return count
  })
</script>

<template>
  <div class="filter-component mb-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <v-btn
        prepend-icon="tabler-filter"
        color="primary"
        size="small"
        variant="outlined"
        @click="toggleFilterPanel"
        class="filter-btn"
      >
        Filter
        <template v-slot:append>
          <v-badge
            v-if="appliedFiltersCount > 0"
            color="error"
            :content="appliedFiltersCount"
            inline
          ></v-badge>
        </template>
      </v-btn>
    </div>

    <v-expand-transition>
      <v-card variant="outlined" v-if="isFilterPanelOpen" class="mb-4">
        <v-card-text>
          <v-row>
            <v-col
              v-if="filters.participant"
              cols="12" sm="6" md="4" >
              <v-select
                :disabled="loadingFilteredChanges"
                v-model="filters.participant"
                :items="filterOptions.participants"
                item-title="text"
                item-value="id"
                label="Participant"
                prepend-inner-icon="tabler-user"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-select
                :disabled="loadingFilteredChanges"
                v-model="filters.field"
                :items="filterOptions.fields"
                item-title="text"
                item-value="id"
                label="Field"
                prepend-inner-icon="tabler-edit"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-select
                :disabled="loadingFilteredChanges"
                v-model="filters.status"
                :items="filterOptions.statuses"
                item-title="text"
                item-value="id"
                label="Status"
                prepend-inner-icon="tabler-circle-check"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-select
                :disabled="loadingFilteredChanges"
                v-model="filters.proposedBy"
                :items="filterOptions.proposedBy"
                item-title="text"
                item-value="id"
                label="Proposed By"
                prepend-inner-icon="tabler-send"
                clearable
                density="compact"
                variant="outlined"
                return-object
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6" md="4">
              <v-menu
                :disabled="loadingFilteredChanges"
                v-model="dateRangeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-bind="props"
                    v-model="dateRangeDisplay"
                    label="Effective Date Range"
                    prepend-inner-icon="tabler-calendar"
                    readonly
                    density="compact"
                    variant="outlined"
                    clearable
                    @click:clear="clearDateRangeFilter"
                  ></v-text-field>
                </template>

                <v-card min-width="300px">
                  <v-card-text>
                    <v-row>
                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredChanges"
                          v-model="filters.effectiveDateRange.startDate"
                          label="Start Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>

                      <v-col cols="12">
                        <v-text-field
                          :disabled="loadingFilteredChanges"
                          v-model="filters.effectiveDateRange.endDate"
                          label="End Date"
                          type="date"
                          density="compact"
                          variant="outlined"
                          hide-details
                        />
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn @click="clearDateRangeFilter" color="primary">
                      Clear
                    </v-btn>
                    <v-btn color="success" @click="dateRangeMenu = false">
                      Apply
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-menu>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            size="small"
            prepend-icon="tabler-x"
            @click="resetFilters"
            class="clear-btn mx-4"
          >
            Clear All
          </v-btn>
          <v-btn
            :loading="loadingFilteredChanges"
            :disabled="appliedFiltersCount === 0 || loadingFilteredChanges"
            variant="outlined"
            color="primary"
            @click="applyFilters"
          >
            Apply Filters
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-expand-transition>
  </div>
</template>

<style scoped>
  .filter-component {
    width: 100%;
  }
</style>