<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
  import { useAppStore } from '@/stores/app/appStore'
  import { ChangeType } from '@/gql/graphql'

  const props = defineProps({
    field: { type: String, required: true },
    fieldName: { type: String, required: true },
    currentValue: { type: String || Number , required: true },
    year: { type: Number, required: true },
    modelValue: { type: Boolean, required: true },
    entityType: { type: String, required: true },
    entityId: { type: String, required: true },
    type: { type: ChangeType, required: true },
    participantName: { type: String, required: false }
  });

  const {state:{loadingCreateChangeProposal}, actions:{createChangeProposal}} = useChangeProposal()

  const appStore = useAppStore()
  const emit = defineEmits(['update:modelValue', 'save', 'close']);

  const newValue = ref(props.currentValue);

  // Initialize date picker value as Date object
  const datePickerValue = ref(new Date(props.year - 1, 11, 31)); // Month is 0-indexed

  // Display format (MM/DD/YYYY)
  const effectiveDate = ref(`12/31/${props.year - 1}`);

  // Menu state for date picker
  const dateMenu = ref(false);

  // Convert Date object to display format
  const formatDateForDisplay = (date: Date) => {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  };

  // Convert display format to Date object
  const formatDateForPicker = (dateString: string) => {
    const [month, day, year] = dateString.split('/');
    return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  };

  // Watch for date picker changes
  watch(datePickerValue, (newDate) => {
    if (newDate instanceof Date) {
      effectiveDate.value = formatDateForDisplay(newDate);
    }
  });

  const isValueUnchanged = computed(() => {
    return newValue.value === props.currentValue;
  });

  const saveChanges = async () =>  {
    await createChangeProposal({
      participantName: props.participantName,
      entityId: props.entityId,
      entityType: props.entityType,
      path: props.field,
      newValue: newValue.value,
      oldValue: props.currentValue,
      effectiveDate: effectiveDate.value,
      type: props.type
    });
    appStore.showSnack('Change proposal created successfully');
    emit('close');
  };

  const closeDialog = () => {
    emit('close');
  };
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 500"
    :model-value="modelValue"
    @update:model-value="(value) => emit('update:modelValue', value)"
  >
    <VCard class="pa-4">
      <VCardTitle class="text-h5 pb-2">
        Edit {{ fieldName }} for {{ entityType }}
      </VCardTitle>

      <VCardText>
        <div class="text-subtitle-1 font-weight-bold mb-1">Current Value</div>
        <VTextField
          :value="currentValue"
          variant="solo-filled"
          hide-details="auto"
          density="comfortable"
          class="mb-4"
          readonly
        />

        <div class="text-subtitle-1 font-weight-bold mb-1">New Value</div>
        <VTextField
          :placeholder="currentValue"
          v-model="newValue"
          :value="newValue"
          variant="outlined"
          hide-details="auto"
          density="comfortable"
          class="mb-1"
          :rules="[(v) => !!v || `${field} is required`]"
        />

        <div v-if="isValueUnchanged" class="text-warning mb-4">
          New value is the same as current value
        </div>

        <div class="text-subtitle-1 font-weight-medium mt-4 mb-1">Effective Date</div>
        <div class="d-flex align-center">
          <VMenu
            v-model="dateMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ props }">
              <VTextField
                v-model="effectiveDate"
                variant="outlined"
                hide-details
                density="comfortable"
                class="flex-grow-1"
                readonly
                v-bind="props"
                prepend-inner-icon="tabler-calendar"
                placeholder="MM/DD/YYYY"
              />
            </template>
            <VDatePicker
              v-model="datePickerValue"
              @update:model-value="dateMenu = false"
              color="primary"
            />
          </VMenu>
          <div v-if="entityType==='changeProposal'" class="ml-3 d-flex align-center text-grey">
            <VIcon icon="tabler-info-circle" class="mr-1" size="small" />
            <span>Default: January 1st of the year</span>
          </div>
        </div>
      </VCardText>

      <VCardActions class="pb-4 px-4">
        <VSpacer />
        <VBtn
          color="secondary"
          @click="closeDialog"
          min-width="100"
        >
          Cancel
        </VBtn>
        <VBtn
          variant="elevated"
          color="primary"
          :loading="loadingCreateChangeProposal"
          @click="saveChanges"
          min-width="100"
          :disabled="!newValue || isValueUnchanged || loadingCreateChangeProposal"
          class="ml-3"
        >
          Save
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>