<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import { formatDistance } from 'date-fns'
  import { useAppStore } from '@/stores/app/appStore'
  import { useNotifications } from '@/composables/notifications/useNotifications'

  const searchQuery = ref('')

  const {
    state: {
      formattedNotifications,
      loadingUserNotifications,
      loadingOperations
    },
    actions: {
      handleMarkAsRead,
      handleMarkAllAsRead,
      refreshNotifications
    }
  } = useNotifications()

  const appStore = useAppStore()

  const notificationsHeader = computed(() => {
    return [
      {
        title: 'Date',
        key: 'createdAt',
        sortable: true
      },
      {
        title: 'Type',
        key: 'type',
        sortable: true
      },
      {
        title: 'Message',
        key: 'message',
        sortable: false
      },
      {
        title: 'From',
        key: 'createdBy',
        sortable: true
      },
      {
        title: 'Status',
        key: 'read',
        sortable: true
      },
      {
        title: 'Actions',
        key: 'actions',
        sortable: false,
      }
    ]
  })

  const handleMarkAllRead = async () => {
    await handleMarkAllAsRead()
    await refreshNotifications()
    appStore.showSnack('All notifications marked as read')
  }

  const onMarkAsRead = async (notificationId: string) => {
    await handleMarkAsRead(notificationId)
    await refreshNotifications()
  }

  const filteredNotifications = computed(() => {
    if (!searchQuery.value) return formattedNotifications.value

    const query = searchQuery.value.toLowerCase()
    return formattedNotifications.value.filter((notification: any) =>
      notification.message.toLowerCase().includes(query) ||
      notification.creatorName.toLowerCase().includes(query) ||
      notification.typeDisplayName.toLowerCase().includes(query)
    )
  })

  const itemsPerPage = ref(10)
  const page = ref(1)

  const totalNotifications = computed(() =>
    filteredNotifications.value ? filteredNotifications.value.length : 0
  )

  const paginatedNotifications = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return filteredNotifications.value.slice(start, end)
  })

  const formatDate = (date: string) => {
    return formatDistance(new Date(date), new Date(), { addSuffix: true })
  }

  onMounted(() => {
    refreshNotifications()

    const refreshTimer = setInterval(() => {
      refreshNotifications()
    }, 2 * 60 * 1000)

    onUnmounted(() => {
      clearInterval(refreshTimer)
    })
  })
</script>

<template>
  <VCard elevation="20" :title="'Notifications'">
    <template v-slot:append>
      <v-btn
        prepend-icon="mdi-check"
        size="small"
        @click="handleMarkAllRead"
        :disabled="loadingOperations || formattedNotifications.length === 0 || !formattedNotifications.some((n: any) => !n.read)"
      >
        {{ 'Mark all as read'}}
      </v-btn>
    </template>

    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          :placeholder="'Search notifications'"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
      </div>
    </VCardText>

    <VDataTable
      :loading="loadingUserNotifications || loadingOperations"
      :headers="notificationsHeader"
      :items="paginatedNotifications"
      :item-value="item => item.id"
      class="px-2"
      hover
    >
      <template #no-data>
        <VCardText>
          <p>{{ 'No notifications available' }}</p>
        </VCardText>
      </template>

      <!-- Type column -->
      <template #[`item.type`]="{ item }">
        <VChip
          :color="item.type === 'SYSTEM' ? 'warning' : item.type === 'MESSAGE' ? 'info' : item.type === 'CERTIFICATION_REJECTION' ? 'error' : 'primary'"
          size="small"
          class="text-capitalize"
        >
          {{ item.typeDisplayName }}
        </VChip>
      </template>

      <!-- Created At column -->
      <template #[`item.createdAt`]="{ item }">
        <div>
          {{ formatDate(item.createdAt) }}
        </div>
      </template>

      <!-- Created By column -->
      <template #[`item.createdBy`]="{ item }">
        <div>
          {{ item.creatorName }}
        </div>
      </template>

      <!-- Read Status column -->
      <template #[`item.read`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            prepend-icon="mdi-circle"
            v-if="!item.read"
            color="primary"
            size="small"
          >
            {{ 'Unread' }}
          </VChip>
          <VChip
            prepend-icon="tabler-eye"
            v-else
            color="grey"
            size="small"
          >
            {{ 'Read' }}
          </VChip>
        </div>
      </template>

      <!-- Actions column -->
      <template #[`item.actions`]="{ item }">
        <VBtn
          :disabled="item.read || loadingOperations"
          append-icon="tabler-eye"
          :color="!item.read ? 'primary' : 'secondary'"
          density="compact"
          @click="() => onMarkAsRead(item.id)"
          class="mx-1"
          size="small"
        >
          {{ 'Mark as read' }}
        </VBtn>
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalNotifications) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalNotifications / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalNotifications / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                {{ 'Previous' }}
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                {{ 'Next'}}
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>

    <VDivider />
  </VCard>
</template>

<style lang="scss">
  .chip-wrapper {
    display: flex;
    align-items: center;

    .v-chip {
      margin-right: 8px;
    }
  }
</style>