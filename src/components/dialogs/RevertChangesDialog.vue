<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useCertifiedDataByYearAndYearBefore } from '@/composables/certified-data/useCertifiedDataByYearAndYearBefore'
import { useAppStore } from '@/stores/app/appStore'

interface Props {
  modelValue: boolean
  certificationYear: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'reverted'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const appStore = useAppStore()

const {
  state: {
    loadingPreviewRevertChanges,
    loadingRevertChanges
  },
  actions: {
    handlePreviewRevertChanges,
    handleRevertAllChanges
  }
} = useCertifiedDataByYearAndYearBefore()

const previewData = ref<any>(null)
const isLoading = ref(false)

// Computed property for dialog visibility
const isDialogOpen = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})

watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    await fetchPreviewData()
  } else {
    previewData.value = null
  }
})

// Helper function to format entity type names
const formatEntityType = (entityType: string): string => {
  const typeMap: Record<string, string> = {
    'certifiedPersonalInfo': 'Personal Info',
    'certifiedPartnerInfo': 'Partner Info',
    'certifiedChild': 'Child',
    'certifiedAddress': 'Address',
    'certifiedSalaryPensionBase': 'Salary Pension Base',
    'certifiedPensionCalculationBase': 'Pension Calculation Base',
    'certifiedBasicInformation': 'Basic Information',
    'certifiedParticipantInformation': 'Participant Information'
  }
  return typeMap[entityType] || entityType.replace(/([A-Z])/g, ' $1').trim()
}

// Helper function to format field names
const formatFieldName = (fieldName: string): string => {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim()
}

// Computed property to check if there are any entities affected
const hasAffectedEntities = computed(() => {
  return previewData.value?.affectedEntities?.length > 0
})

const fetchPreviewData = async () => {
  try {
    isLoading.value = true
    const result = await handlePreviewRevertChanges()
    console.log('Preview result:', result) // Debug log
    previewData.value = result
  } catch (error) {
    console.error('Error fetching preview data:', error)
    appStore.showSnack('Error loading preview data')
  } finally {
    isLoading.value = false
  }
}

const handleRevert = async () => {
  try {
    const result = await handleRevertAllChanges()
    if (result?.success) {
      appStore.showSnack('Changes reverted successfully')
      emit('reverted')
      closeDialog()
    } else {
      appStore.showSnack('Error reverting changes')
    }
  } catch (error) {
    console.error('Error reverting changes:', error)
    appStore.showSnack('Error reverting changes')
  }
}

const closeDialog = () => {
  isDialogOpen.value = false
}
</script>

<template>
  <v-dialog v-model="isDialogOpen" max-width="800px" persistent>
    <v-card>
      <v-card-title class="text-h5 pa-4">
        Revert All Changes Preview
      </v-card-title>
      
      <v-card-text class="pa-4">
        <!-- Loading state -->
        <div v-if="isLoading || loadingPreviewRevertChanges" class="text-center py-8">
          <v-progress-circular indeterminate size="64" color="primary"></v-progress-circular>
          <div class="mt-4 text-body-1">Loading preview data...</div>
        </div>

        <!-- Preview data -->
        <template v-else-if="previewData">
          <div class="text-subtitle-1 font-weight-bold mb-4">
            {{ previewData.estimatedImpact }}
          </div>

          <div class="mb-4">
            <v-row>
              <v-col cols="4">
                <v-card variant="outlined" class="pa-3 text-center">
                  <div class="text-h6 text-primary">{{ previewData.totalCertifiedDataRecords }}</div>
                  <div class="text-caption">Total Records</div>
                </v-card>
              </v-col>
              <v-col cols="4">
                <v-card variant="outlined" class="pa-3 text-center">
                  <div class="text-h6 text-warning">{{ previewData.totalEntitiesAffected }}</div>
                  <div class="text-caption">Entities Affected</div>
                </v-card>
              </v-col>
              <v-col cols="4">
                <v-card variant="outlined" class="pa-3 text-center">
                  <div class="text-h6 text-error">{{ previewData.totalRejectionReasons }}</div>
                  <div class="text-caption">Rejection Reasons</div>
                </v-card>
              </v-col>
            </v-row>
          </div>

          <v-expansion-panels v-if="hasAffectedEntities" class="mb-4">
            <v-expansion-panel>
              <v-expansion-panel-title>
                <div class="d-flex align-center">
                  <v-icon class="mr-2">mdi-account-group</v-icon>
                  <span>Affected Entities ({{ previewData.affectedEntities.length }})</span>
                </div>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <div class="mt-4">
                  <v-card
                    v-for="entity in previewData.affectedEntities"
                    :key="entity.entityId"
                    variant="outlined"
                    class="mb-4"
                  >
                    <v-card-title class="pb-2">
                      <div class="d-flex align-center">
                        <v-avatar color="primary" size="small" class="mr-3">
                          <v-icon size="small">mdi-account</v-icon>
                        </v-avatar>
                        <div>
                          <div class="text-h6">{{ formatEntityType(entity.entityType) }}</div>
                        </div>
                      </div>
                    </v-card-title>

                    <v-card-text class="pt-0">
                      <div class="mb-3">
                        <v-chip
                          v-if="entity.approvedChangesCount > 0"
                          color="success"
                          size="small"
                          class="mr-2 mb-1"
                        >
                          <v-icon start size="small">mdi-check</v-icon>
                          {{ entity.approvedChangesCount }} Approved
                        </v-chip>
                        <v-chip
                          v-if="entity.requestedChangesCount > 0"
                          color="warning"
                          size="small"
                          class="mr-2 mb-1"
                        >
                          <v-icon start size="small">mdi-clock</v-icon>
                          {{ entity.requestedChangesCount }} Requested
                        </v-chip>
                      </div>

                      <!-- Approved Changes -->
                      <div v-if="entity.approvedChanges?.length" class="mb-3">
                        <div class="text-subtitle-2 text-success mb-2">
                          <v-icon size="small" class="mr-1">mdi-check-circle</v-icon>
                          Approved Changes
                        </div>
                        <div class="ml-6">
                          <v-chip
                            v-for="field in entity.approvedChanges"
                            :key="field"
                            color="success"
                            variant="outlined"
                            size="small"
                            class="mr-1 mb-1"
                          >
                            {{ formatFieldName(field) }}
                          </v-chip>
                        </div>
                      </div>

                      <!-- Requested Changes -->
                      <div v-if="entity.requestedChanges?.length" class="mb-3">
                        <div class="text-subtitle-2 text-warning mb-2">
                          <v-icon size="small" class="mr-1">mdi-clock-outline</v-icon>
                          Requested Changes
                        </div>
                        <div class="ml-6">
                          <v-chip
                            v-for="field in entity.requestedChanges"
                            :key="field"
                            color="warning"
                            variant="outlined"
                            size="small"
                            class="mr-1 mb-1"
                          >
                            {{ formatFieldName(field) }}
                          </v-chip>
                        </div>
                      </div>
                    </v-card-text>
                  </v-card>
                </div>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>

          <v-alert 
            type="warning" 
            variant="tonal" 
            class="mb-4"
          >
            <v-icon slot="prepend">mdi-alert-triangle</v-icon>
            <strong>Warning:</strong> This action will revert all approved and rejected changes for the certification year {{ certificationYear }}. This action cannot be undone.
          </v-alert>
        </template>

        <!-- No data state -->
        <div v-else class="text-center py-8">
          <v-icon size="64" color="grey">mdi-information-outline</v-icon>
          <div class="mt-4 text-body-1">No preview data available</div>
        </div>

        <!-- No affected entities state -->
        <v-alert
          v-if="previewData && !hasAffectedEntities"
          type="info"
          variant="tonal"
          class="mb-4"
        >
          <v-icon slot="prepend">mdi-information</v-icon>
          <strong>No Changes to Revert:</strong> There are no approved or requested changes for the certification year {{ certificationYear }}.
        </v-alert>
      </v-card-text>

      <v-card-actions class="pa-4">
        <v-spacer></v-spacer>
        <v-btn 
          color="grey" 
          variant="text" 
          @click="closeDialog"
          :disabled="loadingRevertChanges"
        >
          Cancel
        </v-btn>
        <v-btn
          color="error"
          variant="flat"
          @click="handleRevert"
          :loading="loadingRevertChanges"
          :disabled="!previewData || isLoading || !hasAffectedEntities"
        >
          <v-icon class="mr-2">mdi-undo</v-icon>
          Revert All Changes
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
