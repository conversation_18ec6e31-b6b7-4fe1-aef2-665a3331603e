<template>
  <v-container class="pricing-plans">
    <v-row>
      <v-col cols="12" md="6">
        <v-card class="pricing-card" outlined>
          <v-card-title class="plan-title">{{ starter.title }}</v-card-title>
          <v-card-subtitle class="plan-subtitle">{{ starter.subtitle }}</v-card-subtitle>

          <v-card-text>
            <div class="save-badge">Save up to 20%</div>
            <div class="price">$ {{ starter.price }} / Month</div>

            <v-btn
              color="primary"
              block
              class="my-4"
              @click="emit('select-plan', 'starter')"
            >
              Get started →
            </v-btn>

            <div class="features-title">Featured Include:</div>
            <ul class="features-list">
              <li v-for="(feature, index) in starter.features" :key="index">
                <v-icon small>mdi-checkbox-blank-outline</v-icon> {{ feature }}
              </li>
            </ul>

            <p class="plan-description">{{ starter.description }}</p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card class="pricing-card" outlined>
          <v-card-title class="plan-title">{{ premium.title }}</v-card-title>
          <v-card-subtitle class="plan-subtitle">{{ premium.subtitle }}</v-card-subtitle>

          <v-card-text>
            <div class="save-badge">Save up to 20%</div>
            <div class="price">$ {{ premium.price }} / Month</div>

            <v-btn
              color="primary"
              block
              class="my-4"
              @click="emit('select-plan', 'premium')"
            >
              Get started →
            </v-btn>

            <div class="features-title">Everything in Starter, plus:</div>
            <ul class="features-list">
              <li v-for="(feature, index) in premium.features" :key="index">
                <v-icon small>mdi-checkbox-blank-outline</v-icon> {{ feature }}
              </li>
            </ul>

            <p class="plan-description">{{ premium.description }}</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <div class="text-center mt-6">
      <v-btn text color="primary" @click="emit('compare-plans')">
        See compare
      </v-btn>
    </div>
  </v-container>
</template>

<script setup>
  const props = defineProps({
    starter: {
      type: Object,
      required: true,
      default: () => ({
        title: "Starter",
        subtitle: "Automate time tracking",
        price: 15,
        features: [
          '50 projects',
          '3 teams'
        ],
        description: 'Track your work automatically, get AI to draft your timesheets, set automatic reminders and report effortlessly.'
      })
    },
    premium: {
      type: Object,
      required: true,
      default: () => ({
        title: "Premium",
        subtitle: "Manage projects and profitability",
        price: 28,
        features: [
          'Unlimited projects',
          'Unlimited teams'
        ],
        description: 'Access live project dashboards, set recurring budgets, manage capacity, lock billed hours, and sync.'
      })
    }
  })

  const emit = defineEmits(['select-plan', 'compare-plans'])
</script>

<style scoped>
  .pricing-plans {
    max-width: 1000px;
    margin: 0 auto;
  }

  .pricing-card {
    height: 100%;
    border-radius: 8px;
  }

  .plan-title {
    font-size: 1.5rem;
    font-weight: 600;
    padding-bottom: 0;
  }

  .plan-subtitle {
    font-size: 1rem;
    padding-top: 0;
  }

  .save-badge {
    background-color: #E3F2FD;
    color: #1976D2;
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 12px;
  }

  .price {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 8px 0;
  }

  .features-title {
    font-weight: 600;
    margin: 16px 0 8px;
  }

  .features-list {
    list-style-type: none;
    padding-left: 0;
  }

  .features-list li {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }

  .features-list li .v-icon {
    margin-right: 8px;
  }

  .plan-description {
    margin-top: 16px;
    color: #616161;
  }
</style>