<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { useChangeProposalStore } from '@/stores/change-proposals/changeProposalStore';
  import { useChangeProposal } from '@/composables/change-proposals/useChangeProposal'
  import { usePensionStore } from '@/stores/pension/pensionStore'

  // Use the store instead of props
  const store = useChangeProposalStore();
  const pensionStore = usePensionStore();
  const emit = defineEmits(['close', 'confirm']);

  const selectedChange = ref<'latest' | 'new' | null>(null);

  // Computed property for dialog visibility based on store state
  const isDialogOpen = computed({
    get: () => store.isCompareDialogOpen,
    set: (value) => {
      if (!value) {
        closeDialog();
      }
    }
  });


  const {
    actions: { handleApproveChangeProposal }
  } = useChangeProposal();


  const activeProposal = computed(() => store.getActiveProposal);

  // Check if we have a latest approved change
  const hasLatestChange = computed(() => {
    const latestApprovalChange = store.getLatestApprovalChange;
    return !!latestApprovalChange && !!latestApprovalChange.changes && latestApprovalChange.changes.length > 0;
  });

  // Watch for dialog opening to set default selection
  watch(() => store.isCompareDialogOpen, (newVal) => {
    if (newVal) {
      // If there's no latest change, automatically select the new change
      // Otherwise, select the most recent one based on effective date
      selectedChange.value = !hasLatestChange.value ? 'new' : (isLatestMostRecent.value ? 'latest' : 'new');
    }
  });

  // Process latest approved change data
  const latestChange = computed(() => {
    const latestApprovalChange = store.getLatestApprovalChange;
    if (!latestApprovalChange) return {};

    return {
      id: latestApprovalChange.id,
      entityType: latestApprovalChange.entityType,
      path: latestApprovalChange.changes?.[0]?.path || '-',
      newValue: latestApprovalChange.changes?.[0]?.newValue || '-',
      effectiveDate: latestApprovalChange.effectiveDate,
      reviewedBy: latestApprovalChange.reviewedBy,
      reviewedAt: latestApprovalChange.reviewedAt,
      createdAt: latestApprovalChange.createdAt
    };
  });

  // Process new proposed change data
  const newChange = computed(() => {
    if (!activeProposal.value?.rawData?.proposal) return {};

    return {
      id: activeProposal.value.rawData.proposal.id,
      entityType: activeProposal.value.rawData.proposal.entityType,
      path: activeProposal.value.rawData.change?.path || activeProposal.value.field || '-',
      newValue: activeProposal.value.rawData.change?.newValue || activeProposal.value.newValue || '-',
      effectiveDate: activeProposal.value.rawData.proposal.effectiveDate || activeProposal.value.effectiveDate,
      proposedBy: activeProposal.value.rawData.proposal.createdBy || {
        email: activeProposal.value.proposedBy
      },
      createdAt: activeProposal.value.rawData.proposal.createdAt
    };
  });

  // Determine which change is most recent based on effective date
  const isLatestMostRecent = computed(() => {
    if (!hasLatestChange.value || !latestChange.value.effectiveDate || !newChange.value.effectiveDate) return false;

    const latestDate = new Date(latestChange.value.effectiveDate);
    const newDate = new Date(newChange.value.effectiveDate);

    return latestDate >= newDate;
  });

  const entityTitle = computed(() => {
    const latestApprovalChange = store.getLatestApprovalChange;
    // const activeProposal = store.getActiveProposal;

    const latestName = latestApprovalChange?.participantName;
    const newName = activeProposal.value?.participant || activeProposal.value?.rawData?.proposal?.participantName;

    return `${latestChange.value.entityType || newChange.value.entityType} - ${latestName || newName || ''}`;
  });

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatValue = (value: any) => {
    if (value === null || value === undefined) {
      return '-';
    }

    if (typeof value === 'object' && value !== null) {
      if (value.day && value.month && value.year) {
        // Format date object
        return new Date(value.year, value.month - 1, value.day).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
      return '[object Object]';
    }
    return String(value);
  };

  const closeDialog = () => {
    store.closeCompareDialog();
    store.resetCompareData();
    emit('close');
  };

  const confirmSelection = async ()  => {
    if (!selectedChange.value) return;

    const selectedData = selectedChange.value === 'latest'
      ? latestChange.value
      : newChange.value;
    const isPropagated = selectedChange.value === 'new';
    const propId = activeProposal.value?.rawData?.proposal?.id || newChange.value.id;

    await handleApproveChangeProposal(propId, isPropagated);
    console.log('selectedChange', propId, isPropagated, selectedChange.value);

    closeDialog();
  };
</script>

<template>
  <v-dialog
    v-model="isDialogOpen"
    max-width="1000px"
    persistent
  >
    <v-card>
      <v-card-title class="d-flex align-center">
        <span class="text-h6">{{ hasLatestChange ? 'Approve Changes' : 'New Proposed Change' }}</span>
        <v-spacer></v-spacer>
        <v-btn
          variant="text"
          icon="tabler-close"
          @click="closeDialog"
        ></v-btn>
      </v-card-title>

      <v-divider></v-divider>

      <div class="ma-4" v-if="hasLatestChange">
        <v-alert
          border="start"
          color="primary"
          variant="tonal"
          class="py-4"
        >
          <div class="d-flex flex-column ga-2">
            <div class="text-body-1">
              Change on <span class="font-weight-bold">email</span> for data to be certified will be approved with effective date
              <span class="font-weight-bold text-primary">Dec 31, {{pensionStore.middleColumnYear }}</span>.
            </div>

            <div class="text-caption">
              <span class="font-italic">Note:</span> The database contains changes with effective date after Dec 31, {{pensionStore.middleColumnYear}}.
            </div>

            <v-divider class="my-2"></v-divider>

            <div class="text-body-2 font-weight-medium">
              Please select below:
            </div>

            <ul class="pl-4" style="list-style-type: disc;">
              <li class="text-body-2 mb-1">
                To approve the change <span class="font-weight-bold">only</span> for the data to be certified (Dec 31, {{pensionStore.middleColumnYear}}),
                select <span class="font-weight-bold text-primary">'Keep Latest Approved Change'</span>
              </li>
              <li class="text-body-2">
                To apply the change to the current Year <span class="font-weight-bold text-primary">{{pensionStore?.rightColumnYear}}</span>,
                select <span class="font-weight-bold text-primary">'Apply Change to current Year {{pensionStore?.rightColumnYear}}'</span>
              </li>
            </ul>
          </div>
        </v-alert>
      </div>

      <v-card-text class="pt-4">
        <v-skeleton-loader
          v-if="store.isLoading"
          type="table"
        ></v-skeleton-loader>
        <div v-else>
          <h3 class="mb-3">{{ entityTitle }}</h3>

          <v-row>
            <!-- Only show this column if there is a latest approved change -->
            <v-col cols="12" :md="hasLatestChange ? 6 : 12" v-if="hasLatestChange">
              <v-card
                class="mb-4"
                :class="{ 'selected-change': selectedChange === 'latest' }"
                @click="selectedChange = 'latest'"
                outlined
              >
                <v-card-title class="d-flex align-center">
                  <v-icon left>mdi-history</v-icon>
                  <span>Latest Approved Change</span>
                  <v-spacer></v-spacer>
                  <v-chip
                    v-if="isLatestMostRecent"
                    small
                    color="green"
                    text-color="white"
                  >
                    Most Recent
                  </v-chip>
                </v-card-title>
                <v-divider></v-divider>
                <v-card-text>
                  <div class="mb-2">
                    <div class="text-caption text-grey">Entity Type:</div>
                    <div>{{ latestChange.entityType }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Changed Field:</div>
                    <div>{{ latestChange.path }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Value:</div>
                    <div>{{ formatValue(latestChange.newValue) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Effective Date:</div>
                    <div>{{ formatDate(latestChange.effectiveDate) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Approved By:</div>
                    <div v-if="latestChange.reviewedBy">
                      {{ latestChange.reviewedBy.firstname }} {{ latestChange.reviewedBy.lastname }}
                      ({{ latestChange.reviewedBy.email }})
                    </div>
                    <div v-else>N/A</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Approval Date:</div>
                    <div>{{ formatDate(latestChange.reviewedAt) }}</div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- Always show the new proposed change, adjust width based on whether there's a latest change -->
            <v-col cols="12" :md="hasLatestChange ? 6 : 12">
              <v-card
                class="mb-4"
                :class="{ 'selected-change': selectedChange === 'new' }"
                @click="selectedChange = 'new'"
                outlined
              >
                <v-card-title class="d-flex align-center">
                  <v-icon left>mdi-pencil</v-icon>
                  <span>New Proposed Change</span>
                  <v-spacer></v-spacer>
                  <v-chip
                    v-if="!isLatestMostRecent && hasLatestChange"
                    small
                    color="green"
                    text-color="white"
                  >
                    Most Recent
                  </v-chip>
                  <v-chip
                    v-if="!hasLatestChange"
                    small
                    color="blue"
                    text-color="white"
                  >
                    First Change
                  </v-chip>
                </v-card-title>
                <v-divider></v-divider>
                <v-card-text>
                  <div class="mb-2">
                    <div class="text-caption text-grey">Entity Type:</div>
                    <div>{{ newChange.entityType }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Changed Field:</div>
                    <div>{{ newChange.path }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Value:</div>
                    <div>{{ formatValue(newChange.newValue) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Effective Date:</div>
                    <div>{{ formatDate(newChange.effectiveDate) }}</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Proposed By:</div>
                    <div v-if="newChange.proposedBy">
                      {{ newChange.proposedBy.firstname }} {{ newChange.proposedBy.lastname }}
                      ({{ newChange.proposedBy.email }})
                    </div>
                    <div v-else>N/A</div>
                  </div>

                  <div class="mb-2">
                    <div class="text-caption text-grey">Proposal Date:</div>
                    <div>{{ formatDate(newChange.createdAt) }}</div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <v-card class="mt-4" outlined>
            <v-card-title>Selected Change Summary for current year {{  }}</v-card-title>
            <v-divider></v-divider>
            <v-card-text>
              <template v-if="selectedChange === 'latest'">
                <div class="mb-2">
                  <span class="font-weight-medium">You have selected the latest approved change:</span>
                </div>
                <div class="mb-1">
                  <span class="text-grey">Field:</span> {{ latestChange.path }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Value:</span> {{ formatValue(latestChange.newValue) }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Effective Date:</span> {{ formatDate(latestChange.effectiveDate) }}
                </div>
              </template>

              <template v-else-if="selectedChange === 'new'">
                <div class="mb-2">
                  <span class="font-weight-medium">You have selected the new proposed change:</span>
                </div>
                <div class="mb-1">
                  <span class="text-grey">Field:</span> {{ newChange.path }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Value:</span> {{ formatValue(newChange.newValue) }}
                </div>
                <div class="mb-1">
                  <span class="text-grey">Effective Date:</span> {{ formatDate(newChange.effectiveDate) }}
                </div>
              </template>

              <template v-else>
                <div class="text-grey font-italic">Please select one of the changes above</div>
              </template>
            </v-card-text>
          </v-card>
        </div>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions class="pa-4">
        <v-spacer></v-spacer>
        <v-btn
          variant="tonal"
          color="secondary"
          @click="closeDialog"
          class="mr-2"
        >
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          variant="tonal"
          :disabled="!selectedChange"
          @click="confirmSelection"
        >
          {{ hasLatestChange ? 'Confirm Selection' : 'Approve Change' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>



<style scoped>
  .selected-change {
    border: 2px solid #1976d2 !important;
    background-color: #f5f9ff;
  }

  .v-card {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .v-card:hover {
    background-color: #f5f5f5;
  }
</style>

