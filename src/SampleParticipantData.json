{"__typename": "Participant", "certifiedData": [{"__typename": "CertifiedData", "certificationYear": 2022, "certifiedAt": "2025-04-15T07:06:59.000Z", "certifiedById": "be196c0f-ee7b-42a3-9163-885f649e65ef", "certifiedEmploymentInfo": {"__typename": "CertifiedEmploymentInfo", "department": "HR", "employeeId": "234234", "havNum": 34, "id": "0250e8fb-7572-497d-95b4-de90671f39da", "position": "Admin", "regNum": 34, "startDate": "2007-05-01T18:33:55.000Z", "status": "Active"}, "id": "49224a93-727c-4be5-8037-0859bf4e1315", "certifiedIndexationStartOfYear": null, "notes": "asdfasdf", "participantId": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "certifiedPensionCorrections": null, "certifiedPensionInfo": null, "certifiedPensionParameters": {"__typename": "CertifiedPensionParameters", "id": "abb57a7e-48b6-4c69-8d72-70742b4047b7", "year": "2023"}, "certifiedPersonalInfo": {"__typename": "CertifiedPersonalInfo", "id": "4f3f0f56-5888-4467-9a03-759695acf8a6"}, "certifiedVoluntaryContributions": {"__typename": "CertifiedVoluntaryContributions", "id": "fe644238-c09e-41f0-8cc4-417466bcd212"}}, {"__typename": "CertifiedData", "certificationYear": 2023, "certifiedAt": "2025-04-15T07:06:59.000Z", "certifiedById": "be196c0f-ee7b-42a3-9163-885f649e65ef", "certifiedEmploymentInfo": {"__typename": "CertifiedEmploymentInfo", "department": "HR", "employeeId": "234234", "havNum": 34, "id": "e21afa0b-f0dd-4302-b628-21014e6c5db0", "position": "Admin", "regNum": 34, "startDate": "2007-05-01T18:33:55.000Z", "status": "Active"}, "id": "c460fc44-3f07-4296-9473-bcbb11197344", "certifiedIndexationStartOfYear": {"__typename": "CertifiedIndexationStartOfYear", "id": "81f67b9f-14dd-422a-9599-c2ff3181e38d"}, "notes": "asdfasdf", "participantId": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "certifiedPensionCorrections": {"__typename": "CertifiedPensionCorrections", "id": "cd545c32-4f8d-4dea-8b45-eb7d77af779f", "year": "2024"}, "certifiedPensionInfo": {"__typename": "CertifiedPensionInfo", "id": "bbbce2f6-b469-487b-bca3-8d7cc2ef0a60", "code": 3, "codeDescription": "asdf as"}, "certifiedPensionParameters": {"__typename": "CertifiedPensionParameters", "id": "d21dc0af-b608-4663-b722-329ea0b8f9cd", "year": "2023"}, "certifiedPersonalInfo": {"__typename": "CertifiedPersonalInfo", "id": "db8cef4a-9b23-40ee-9d02-e6b77cc951df"}, "certifiedVoluntaryContributions": {"__typename": "CertifiedVoluntaryContributions", "id": "2990f4ce-fb04-4541-9a17-9d0b186e5abf"}}, {"__typename": "CertifiedData", "certificationYear": 2024, "certifiedAt": "2025-04-15T07:06:59.000Z", "certifiedById": "be196c0f-ee7b-42a3-9163-885f649e65ef", "certifiedEmploymentInfo": {"__typename": "CertifiedEmploymentInfo", "department": "HR", "employeeId": "234234", "havNum": 34, "id": "78963f2f-facb-4616-bec0-35ef1fa4f67b", "position": "Admin", "regNum": 34, "startDate": "2007-05-01T18:33:55.000Z", "status": "Active"}, "id": "47836831-8723-464e-97db-23de1d13c7d2", "certifiedIndexationStartOfYear": {"__typename": "CertifiedIndexationStartOfYear", "id": "a4061a86-9086-4bda-a60f-a8719908557a"}, "notes": "asdfasdf", "participantId": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "certifiedPensionCorrections": {"__typename": "CertifiedPensionCorrections", "id": "0cfdeea7-aa55-48de-a177-ccbbcaadf9b6", "year": "2024"}, "certifiedPensionInfo": {"__typename": "CertifiedPensionInfo", "id": "fd346c8f-78c7-4922-a8aa-6305c89a3136", "code": 3, "codeDescription": "asdf as"}, "certifiedPensionParameters": {"__typename": "CertifiedPensionParameters", "id": "5b072186-6509-44e4-b93c-524f4d7fa540", "year": "2024"}, "certifiedPersonalInfo": {"__typename": "CertifiedPersonalInfo", "id": "ca176df6-f999-414b-8969-0d14fc1913a0"}, "certifiedVoluntaryContributions": {"__typename": "CertifiedVoluntaryContributions", "id": "0c6b3667-6d9a-4ee9-89a0-40a80381676e"}}], "changeProposals": null, "documents": [], "employmentInfo": {"__typename": "EmploymentInfo", "department": "Marketing", "employeeId": "S232", "havNum": 1, "id": "1f7e910d-ed48-47f4-820e-0a1183a104f9", "position": "sdg", "regNum": 45, "startDate": "2007-05-01T10:02:23.000Z", "status": "Active", "salaryEntries": [{"__typename": "SalaryEntry", "id": "3ab07dce-8334-46d8-8703-db9049f60607", "year": 2023, "amount": 4555, "partTimePercentage": 1, "pendingChanges": []}, {"__typename": "SalaryEntry", "id": "aafcb0ec-90bc-4f9a-aba1-e0d1ac2e5173", "year": 2022, "amount": 4430, "partTimePercentage": 1, "pendingChanges": []}, {"__typename": "SalaryEntry", "id": "4658003e-4592-4d2b-a558-b52435e88312", "year": 2024, "amount": 4680, "partTimePercentage": 1, "pendingChanges": []}, {"__typename": "SalaryEntry", "id": "545caee4-d671-4a12-814c-b134a063fd83", "year": 2025, "amount": 2490, "partTimePercentage": 1, "pendingChanges": []}]}, "pensionData": null, "pensionInfo": {"__typename": "PensionInfo", "code": 233, "codeDescription": "sdfgsdf", "id": "aa3924cb-aa6f-42fb-8be4-502226a881ec", "accruedGrossAnnualOldAgePension": 244, "accruedGrossAnnualPartnersPension": 109, "accruedGrossAnnualSinglesPension": 110, "attainableGrossAnnualOldAgePension": 485, "extraAccruedGrossAnnualOldAgePension": 67, "extraAccruedGrossAnnualPartnersPension": 67, "grossAnnualDisabilityPension": 67, "pensionBase": 456456, "pendingChanges": []}, "personalInfo": {"__typename": "PersonalInfo", "birthDay": 26, "birthMonth": 11, "birthYear": 1984, "email": null, "firstName": "Jasonwew", "id": "d7556483-026b-4ecd-bd75-1fe6a90619da", "lastName": "<PERSON>", "maritalStatus": "Married", "participantId": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "phone": "+234234234", "pendingChanges": null, "partnerInfo": [{"__typename": "PartnerInfo", "id": "500f0356-2893-4635-b6f0-cea54311cb27", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON>", "dateOfBirth": "1940-01-01T00:00:00.000Z", "isCurrent": true, "startDate": "2020-01-01T00:00:00.000Z", "pendingChanges": []}, {"__typename": "PartnerInfo", "id": "cda62888-f660-4c60-85fe-cad8c19a6cea", "lastName": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "dateOfBirth": "1980-01-01T00:00:00.000Z", "isCurrent": false, "startDate": "2020-01-01T00:00:00.000Z", "pendingChanges": []}, {"__typename": "PartnerInfo", "id": "56e52f84-fe3d-4af1-9996-00cae8eeed9a", "lastName": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON>", "dateOfBirth": "1980-01-01T00:00:00.000Z", "isCurrent": false, "startDate": "2020-01-01T00:00:00.000Z", "pendingChanges": []}, {"__typename": "PartnerInfo", "id": "ce2fb415-121b-4b71-b6c4-310e3eaddb58", "lastName": "<PERSON><PERSON><PERSON>", "firstName": "Adrinllte", "dateOfBirth": "1980-01-01T00:00:00.000Z", "isCurrent": false, "startDate": "2020-01-01T00:00:00.000Z", "pendingChanges": []}], "children": [{"__typename": "Child", "id": "23758b35-bdfc-4877-8f6d-a173401425d5", "firstName": "Waist", "lastName": "defsfdgsdf", "dateOfBirth": "2025-03-29T20:49:21.000Z", "isOrphan": true, "isStudying": true, "pendingChanges": []}, {"__typename": "Child", "id": "c7fbcc74-0abe-4064-a0de-6b3dec132f25", "firstName": "Pwer", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "2025-03-29T20:49:48.000Z", "isOrphan": true, "isStudying": true, "pendingChanges": []}, {"__typename": "Child", "id": "677e6c07-8757-41ff-ad80-ed148888877d", "firstName": "wwertwersdf ", "lastName": "sdfgsdfg", "dateOfBirth": "2025-03-29T20:50:12.000Z", "isOrphan": true, "isStudying": true, "pendingChanges": []}], "address": {"__typename": "Address", "id": "3b665348-2f57-4a89-8ecc-04dacbe650cd", "street": "<PERSON><PERSON><PERSON><PERSON>", "houseNumber": "234234", "postalCode": "234234", "city": "<PERSON>", "state": "asdfasdf", "country": "<PERSON><PERSON><PERSON>", "pendingChanges": []}}, "createdAt": "2025-03-24T12:39:16.087Z", "createdBy": "be196c0f-ee7b-42a3-9163-885f649e65ef", "id": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "lastModified": "2025-03-24T15:39:10.000Z", "status": "Active", "updatedAt": "2025-03-24T15:39:06.000Z", "updatedBy": "be196c0f-ee7b-42a3-9163-885f649e65ef"}