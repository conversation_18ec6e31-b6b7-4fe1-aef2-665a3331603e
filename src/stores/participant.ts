import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useMutation } from '@vue/apollo-composable'
import gql from 'graphql-tag'

const CREATE_PARTICIPANT = gql`
  mutation CreateParticipant($input: CreateParticipantInput!) {
    createParticipant(input: $input) {
      id
      personalInfo {
        id
        firstName
        lastName
        email
        phone
        maritalStatus
        birthDay
        birthMonth
        birthYear
        address {
          street
          houseNumber
          postalCode
          city
          state
          country
        }
        partnerInfo {
          id
          firstName
          lastName
          dateOfBirth
          startDate
          isCurrent
          isDeceased
        }
        children {
          id
          firstName
          lastName
          dateOfBirth
          isOrphan
          isStudying
        }
      }
      employmentInfo {
        id
        employeeId
        department
        position
        startDate
        status
        salary
      }
      pensionInfo {
        id
        code
        codeDescription
        codeEffectiveDate
      }
      status
      createdAt
      updatedAt
    }
  }
`

export const useParticipantStore = defineStore('participant', () => {
    const loading = ref(false)
    const error = ref<string | null>(null)

    const { mutate: createParticipantMutation } = useMutation(CREATE_PARTICIPANT)

    const createParticipant = async (participantData: any) => {
        loading.value = true
        error.value = null

        const currentUserId = computed(() => "be196c0f-ee7b-42a3-9163-885f649e65ef") // TODO: Get from auth store once you can set in claims


        try {
            const result = await createParticipantMutation({
                input: {
                    status: 'active',
                    createdBy: currentUserId.value, // TODO: Get from auth store
                    updatedBy: currentUserId.value, // TODO: Get from auth store
                    personalInfo: {
                        firstName: participantData.personalInfo.firstName,
                        lastName: participantData.personalInfo.lastName,
                        email: participantData.personalInfo.email,
                        phone: participantData.personalInfo.phone,
                        maritalStatus: participantData.personalInfo.maritalStatus,
                        birthDay: new Date(participantData.personalInfo.birthDate).getDate(),
                        birthMonth: new Date(participantData.personalInfo.birthDate).getMonth() + 1,
                        birthYear: new Date(participantData.personalInfo.birthDate).getFullYear(),
                        address: {
                            street: participantData.personalInfo.address.street,
                            houseNumber: participantData.personalInfo.address.houseNumber,
                            postalCode: participantData.personalInfo.address.postalCode,
                            city: participantData.personalInfo.address.city,
                            state: participantData.personalInfo.address.state,
                            country: participantData.personalInfo.address.country
                        },
                        partnerInfo: participantData.personalInfo.partners.map((partner: any) => ({
                            firstName: partner.firstName,
                            lastName: partner.lastName,
                            dateOfBirth: partner.dateOfBirth,
                            startDate: partner.startDate,
                            isCurrent: partner.isCurrent,
                            isDeceased: partner.isDeceased
                        })),
                        children: participantData.personalInfo.children.map((child: any) => ({
                            firstName: child.firstName,
                            lastName: child.lastName,
                            dateOfBirth: child.dateOfBirth,
                            isOrphan: child.isOrphan,
                            isStudying: child.isStudying
                        }))
                    },
                    employmentInfo: {
                        employeeId: participantData.employmentInfo.employeeId,
                        department: participantData.employmentInfo.department,
                        position: participantData.employmentInfo.position,
                        startDate: participantData.employmentInfo.startDate,
                        status: participantData.employmentInfo.status,
                        salary: parseFloat(participantData.employmentInfo.salary)
                    },
                    pensionInfo: {
                        code: participantData.pensionInfo.code,
                        codeDescription: participantData.pensionInfo.codeDescription,
                        codeEffectiveDate: participantData.pensionInfo.codeEffectiveDate
                    }
                }
            })

            if (!result?.data) {
                throw new Error('No data returned from mutation')
            }

            return result.data.createParticipant
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'An error occurred while creating the participant'
            throw err
        } finally {
            loading.value = false
        }
    }

    return {
        loading,
        error,
        createParticipant
    }
}) 