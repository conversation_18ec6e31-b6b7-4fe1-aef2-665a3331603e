import { defineStore } from 'pinia'
import {UserInput} from "@/types/user.types";

export const useUserStore = defineStore('userStore', {
    state: () => {
        return {
            userFormDialog: false,
            userFormDetails:  {} as UserInput
        }
    },
    actions: {
        showUserFormDialog() {
            this.userFormDialog = true
        },
        hideUserFormDialog() {
            this.userFormDialog = false
            this.userFormDetails = {} as UserInput
        },
        setUserFormDetails(user: UserInput) {
            this.userFormDetails = {email: user?.email ,id: user?.id,firstname: user.firstname, lastname: user.lastname, roleId: user.role?.id} as UserInput
        },
        resetUserFormDetails() {
            this.userFormDetails = {} as UserInput
        },
        resetStore() {
            this.$reset()
        },
    },
    persist: true
})
