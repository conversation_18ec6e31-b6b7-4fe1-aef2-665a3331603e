import { defineStore } from 'pinia'

export const useAppStore = defineStore('appStore', {
    state: () => {
        return {
            initialized: false,
            initializing: false,
            showSnackbar: false,
            showLoader: false,
            layoutDarkMode: false,
            notifications: [] as any[],
            snackMsg: '',
        }
    },
    actions: {
        showSnack(msg: string) {
            this.showSnackbar = true
            this.snackMsg = msg

            setTimeout(() => {
                this.showSnackbar = false
                this.snackMsg = ''
            }, 3000)
        },
        showAppLoader() {
            this.showLoader = true
        },
        hideAppLoader() {
            this.showLoader = false
        },
        async updateNotifications(notifications: any[]) {
            if (this.notifications.length !== 0) return
            this.notifications = Array.from(
                new Set([...this.notifications, ...notifications]),
            )
        },
        async setMarkedAsRead(notificationId: string) {
            const notification = this.notifications.find(n => n.id === notificationId)
            if (notification) {
                notification.isNew = false
            }
        },
        async pushToNotifications(data: any) {
            const notification = {
                id: data.id,
                emailBody: data.emailNotification?.emailBody,
                emailSubject: data.emailNotification?.emailSubject,
                time: data.updatedAt,
                createdAt: data.createdAt,
                isNew: data.isNew,
            }
            const existingNotification = this.notifications.find(
                n => n.id === notification.id,
            )
            if (!existingNotification) {
                this.notifications.push(notification)
            }
        },
        resetStore() {
            this.$reset()
        },
    },
    persist: true
})
