import { useAuthStore } from '@/stores/auth/authStore';
import { useAppStore } from '@/stores/app/appStore';

/**
 * Initializes all stores that need to be loaded before the application renders the first time.
 */
export const initStores = async () => {
  const appStore = useAppStore();
  if (appStore.initialized || appStore.initializing) {
    return;
  }

  appStore.initializing = true;
  appStore.initialized = true;
  appStore.initializing = false;
};

/**
 * Resets all stores that have been initialized with initStores.
 * All stores that need to be reset should have a resetStore action that handles unsubscribing listeners and resetting the store.
 */
export const resetStores = async () => {
  const appStore = useAppStore();
  const authStore = useAuthStore();


  if (!appStore.initialized) {
    return;
  }

  // Call resetStore action on stores that need to be reset
  // await Promise.all([appStore.resetStore(), authStore.resetStore()]); //TODO: Implement resetStore action in appStore and authStore
};