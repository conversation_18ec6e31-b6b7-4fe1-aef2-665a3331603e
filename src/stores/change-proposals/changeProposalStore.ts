import { defineStore } from 'pinia'

interface ChangeProposalState {
  activeProposal: any | null
  latestApprovalChange: any | null
  isCompareDialogOpen: boolean
  isLoading: boolean
}

export const useChangeProposalStore = defineStore('changeProposal', {
  state: (): ChangeProposalState => ({
    activeProposal: null,
    latestApprovalChange: null,
    isCompareDialogOpen: false,
    isLoading: false
  }),

  getters: {
    getActiveProposal: (state) => state.activeProposal,
    getLatestApprovalChange: (state) => state.latestApprovalChange,
    isDialogOpen: (state) => state.isCompareDialogOpen
  },

  actions: {
    setActiveProposal(proposal: any) {
      this.activeProposal = proposal
    },

    setLatestApprovalChange(change: any) {
      this.latestApprovalChange = change
    },

    openCompareDialog() {
      this.isCompareDialogOpen = true
    },

    closeCompareDialog() {
      this.isCompareDialogOpen = false
    },

    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    resetCompareData() {
      this.activeProposal = null
      this.latestApprovalChange = null
    }
  }
})
