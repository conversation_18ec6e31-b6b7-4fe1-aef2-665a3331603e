import type { User } from 'firebase/auth'
import {
  confirmPasswordReset,
  createUserWithEmailAndPassword,
  onIdTokenChanged,
  reload,
  signOut,
  sendEmailVerification,
  signInWithEmailAndPassword,
  updateProfile,
} from 'firebase/auth'
import type { Unsubscribe } from 'firebase/firestore'
import { watch } from 'vue'
import { initStores, resetStores } from '../initStores'
import { auth } from '@/libs/firebase/config'
import { apolloClient } from '@/api/middleware/apolloClient'
import type { Credentials, PasswordReset } from '@/types/authTypes'
import { router } from '@/plugins/1.router'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null) as Ref<User | null>
  const userId = ref(null) as Ref<string | null>
  const claims = ref({}) as Ref<Record<string, unknown>>
  const initialized = ref(false)

  const isLoggedIn = computed(() => user.value != null && userId.value != null)

  const authStore = useAuthStore()

  let unsubAuthListener: Unsubscribe | null = null

  function initAuth() {
    // Return a promise that resolves when auth is initialized
    return new Promise(resolve => {
      if (unsubAuthListener)
        unsubAuthListener()

      unsubAuthListener = onIdTokenChanged(auth, async user => {
        if (user != null) {
          const token = await user.getIdTokenResult(true)

          localStorage.setItem('uid', token?.claims?.sub || '')
          claims.value = token.claims
          userId.value = user.uid
          authStore.user = user
        }
        else {
          await resetStores()

          authStore.user = null
          userId.value = null
        }

        initialized.value = true
        resolve(user)
      })
    })
  }


  const registerUser = async (credentials: Credentials): Promise<{ uid: string }> => {
    try {
      const userCredential = await createUserWithEmailAndPassword(
          auth,
          credentials.email,
          credentials.password,
      )

      await updateProfile(userCredential.user, {
        displayName: credentials.adminName,
      })
      console.log({ userCredential })

      return { uid: userCredential.user.uid }
    }
    catch (error) {
      console.log(error)

      return { uid: '' }
    }
  }

  const signInUser = async (credentials: Credentials) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
          auth,
          credentials.email,
          credentials.password,
      )

      await new Promise(resolve => {
        const stopWatch = watch(authStore.$state, () => {
          if (authStore.user?.uid === userCredential.user.uid) {
            initStores()
            resolve(null)
            stopWatch()
          }
        })
      })
    }
    catch (error) {
      console.log(error)
    }
  }

  const handleSignOut = async () => {
    console.log('signing out')
    try {
      await signOut(auth)
      localStorage.clear()
      await resetStores()
      await apolloClient.resetStore()
      await router.push('/login')
    }
    catch (error) {
      console.log(error)
    }
  }

  const handleSendEmailVerification = async () => {
    const currentUser = auth.currentUser
    if (currentUser) {
      sendEmailVerification(currentUser)
          .then(() => {
            console.log('Email verification sent')
          })
          .catch(error => {
            console.error('Error sending email verification:', error)
          })
    }
  }

  const resetPassword = async (resetCreds: PasswordReset) => {
    const oobCode
        = new URL(window.location.href).searchParams.get('oobCode') || ''

    console.log({ oobCode })
    if (oobCode === '')
      return

    try {
      const resetPass = await confirmPasswordReset(
          auth,
          oobCode,
          resetCreds.password,
      )
    }
    catch (error) {
      console.log(error)
    }
  }

  const refreshUser = async () => {
    if (authStore.user)
      await reload(authStore.user)
  }

  return {
    user,
    userId,
    claims,
    initialized,
    isLoggedIn,
    initAuth,
    registerUser,
    signInUser,
    handleSignOut,
    handleSendEmailVerification,
    resetPassword,
    refreshUser,
  }
})

export const initAuthStore = () => {
  const authStore = useAuthStore()
  authStore.initAuth()
  return authStore
}
